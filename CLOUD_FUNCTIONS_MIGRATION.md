# Firebase Cloud Functions Migration Guide

## Overview

This guide outlines the migration from direct Firebase Storage uploads to Cloud Function-based uploads for enhanced security, validation, and processing.

## What Changed

### Before (Direct Upload)
- Client-side uploads directly to Firebase Storage
- Client-side file validation only
- Basic security rules in Storage
- Limited image processing capabilities

### After (Cloud Function Upload)
- Server-side uploads through Cloud Functions
- Server-side authentication and authorization
- Advanced file validation and processing
- Automatic image optimization and resizing
- Enhanced security and error handling

## Migration Steps

### 1. Deploy Cloud Functions

```bash
# Install dependencies
cd functions
npm install

# Build and deploy
npm run build
npm run deploy
```

### 2. Update Environment Variables

Add to your `.env.local` file:

```env
# For development with emulators
NEXT_PUBLIC_USE_FIREBASE_EMULATORS=true

# Your Firebase project ID (already exists)
NEXT_PUBLIC_FIREBASE_PROJECT_ID=your-project-id
```

### 3. Code Changes

The existing `farmImageService.uploadFarmImage()` and `profileImageService.uploadProfileImage()` methods now automatically use Cloud Functions. No changes needed in components!

### 4. Backward Compatibility

During transition, you can use the direct upload methods:

```typescript
// Use Cloud Function (default)
await farmImageService.uploadFarmImage(farmId, file, options)

// Use direct upload (fallback)
await farmImageService.uploadFarmImageDirect(farmId, file, options)
```

## Testing

### 1. Local Testing with Emulators

```bash
# Start emulators
cd functions
npm run serve

# In another terminal, start your web app
cd apps/web
npm run dev
```

### 2. Test Upload Functionality

1. Navigate to a page with file upload (e.g., farm creation)
2. Upload an image
3. Verify the upload works and progress is tracked
4. Check the Firebase Storage console for the uploaded file

### 3. Verify Processing

- Images should be automatically resized
- File sizes should be optimized
- Metadata should be preserved

## Rollback Plan

If issues occur, you can quickly rollback by updating the storage service:

```typescript
// In apps/web/src/lib/firebase/storage.ts
export const farmImageService = {
  async uploadFarmImage(farmId: string, file: File, options?: UploadOptions) {
    // Temporarily use direct upload
    return this.uploadFarmImageDirect(farmId, file, options)
  }
}
```

## Monitoring

### 1. Cloud Function Logs

```bash
# View logs
firebase functions:log

# Follow logs in real-time
firebase functions:log --follow
```

### 2. Error Tracking

Monitor the Firebase Console for:
- Function execution errors
- Authentication failures
- File validation errors
- Storage upload failures

### 3. Performance Metrics

Track:
- Function execution time
- Memory usage
- Success/failure rates
- Upload file sizes

## Security Improvements

### 1. Server-side Validation
- File size limits enforced server-side
- MIME type validation
- Malicious file detection

### 2. Enhanced Authorization
- Role-based access control
- Farm ownership verification
- User authentication validation

### 3. Secure File Processing
- Image optimization and sanitization
- Metadata stripping for privacy
- Consistent file naming

## Troubleshooting

### Common Issues

1. **CORS Errors**
   - Check allowed origins in `functions/src/config.ts`
   - Verify domain is included in CORS settings

2. **Authentication Errors**
   - Ensure user is logged in
   - Check Firebase Auth token validity
   - Verify user roles are set correctly

3. **File Size Errors**
   - Check file size limits in Cloud Function config
   - Verify client-side validation matches server-side

4. **Upload Failures**
   - Check Cloud Function logs
   - Verify Firebase Storage permissions
   - Ensure bucket exists and is accessible

### Debug Steps

1. **Check Function Deployment**
   ```bash
   firebase functions:list
   ```

2. **Test Function Directly**
   ```bash
   curl -X POST \
     -H "Authorization: Bearer YOUR_TOKEN" \
     -F "file=@test-image.jpg" \
     -F "bucket=farm-images" \
     -F "farmId=test-farm" \
     https://your-region-your-project.cloudfunctions.net/uploadImage
   ```

3. **Monitor Real-time Logs**
   ```bash
   firebase functions:log --follow
   ```

## Performance Considerations

### 1. Cold Starts
- Cloud Functions may have cold start delays
- Consider keeping functions warm for production

### 2. Memory Usage
- Functions are configured with 1GB memory
- Adjust based on image processing needs

### 3. Timeout Settings
- Functions have 540-second timeout
- Sufficient for large image uploads

### 4. Concurrent Uploads
- Functions can handle multiple concurrent uploads
- Monitor for rate limiting

## Cost Implications

### Cloud Function Costs
- Pay per invocation and execution time
- Memory usage affects pricing
- Consider batch processing for multiple files

### Storage Costs
- Same Firebase Storage costs apply
- Optimized images may reduce storage costs
- Monitor storage usage and cleanup old files

## Next Steps

1. **Monitor Performance**: Track upload success rates and performance
2. **Optimize Processing**: Fine-tune image processing settings
3. **Add Features**: Consider adding thumbnail generation, watermarking
4. **Scale**: Monitor usage and adjust function resources as needed

## Support

For issues or questions:
1. Check the Cloud Function logs
2. Review this migration guide
3. Test with the emulator environment
4. Check Firebase Console for errors
