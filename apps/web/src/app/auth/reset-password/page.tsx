'use client'

import { useState, useEffect, Suspense } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { confirmPasswordReset } from 'firebase/auth'
import { auth } from '@/lib/firebase/client'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'

function ResetPasswordForm() {
  const [password, setPassword] = useState('')
  const [confirmPassword, setConfirmPassword] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState('')
  const [isValid, setIsValid] = useState(false)
  const [isCheckingSession, setIsCheckingSession] = useState(true)
  const router = useRouter()
  const searchParams = useSearchParams()

  useEffect(() => {
    const handlePasswordReset = async () => {
      try {
        // Firebase password reset uses 'oobCode' parameter
        const oobCode = searchParams.get('oobCode')
        const mode = searchParams.get('mode')

        console.log('Firebase reset password URL params:', {
          oobCode: oobCode ? 'present' : 'missing',
          mode,
          allParams: Object.fromEntries(searchParams.entries())
        })

        if (mode === 'resetPassword' && oobCode) {
          // Valid Firebase password reset link
          setIsValid(true)
        } else {
          setError('Invalid or expired reset link')
        }
      } catch (err) {
        console.error('Unexpected error in handlePasswordReset:', err)
        setError('An error occurred while validating the reset link')
      } finally {
        setIsCheckingSession(false)
      }
    }

    handlePasswordReset()
  }, [searchParams])

  const handleResetPassword = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    setError('')

    if (password !== confirmPassword) {
      setError('Passwords do not match')
      setIsLoading(false)
      return
    }

    if (password.length < 6) {
      setError('Password must be at least 6 characters')
      setIsLoading(false)
      return
    }

    try {
      const oobCode = searchParams.get('oobCode')
      if (!oobCode) {
        setError('Invalid reset code')
        return
      }

      await confirmPasswordReset(auth, oobCode, password)
      router.push('/auth/login?message=Password updated successfully')
    } catch (error: unknown) {
      console.error('Password reset error:', error)
      setError(error instanceof Error ? error.message : 'An unexpected error occurred')
    } finally {
      setIsLoading(false)
    }
  }

  if (isCheckingSession || (!isValid && !error)) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-earth-100 to-earth-200 px-4">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-accent-600 mx-auto"></div>
          <p className="mt-4 text-earth-600">Validating reset link...</p>
        </div>
      </div>
    )
  }

  if (error && !isValid) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-earth-100 to-earth-200 px-4">
        <div className="max-w-md w-full space-y-8">
          <div className="text-center">
            <h1 className="text-3xl font-bold text-earth-900 mb-2">Invalid Link</h1>
            <p className="text-earth-600 mb-6">{error}</p>
          </div>

          <div className="bg-white rounded-lg shadow-lg p-8 text-center">
            <Button
              variant="primary"
              size="lg"
              className="w-full"
              onClick={() => router.push('/auth/forgot-password')}
            >
              Request New Reset Link
            </Button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-earth-100 to-earth-200 px-4">
      <div className="max-w-md w-full space-y-8">
        <div className="text-center">
          <h1 className="text-3xl font-bold text-earth-900 mb-2">Reset Your Password</h1>
          <p className="text-earth-600">Enter your new password below</p>
        </div>

        <div className="bg-white rounded-lg shadow-lg p-8">
          <form onSubmit={handleResetPassword} className="space-y-6">
            {error && (
              <div className="bg-red-50 border border-red-200 rounded-md p-3">
                <p className="text-red-600 text-sm">{error}</p>
              </div>
            )}

            <div>
              <label htmlFor="password" className="block text-sm font-medium text-earth-700 mb-2">
                New Password
              </label>
              <Input
                id="password"
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                placeholder="Enter new password (min. 6 characters)"
                required
              />
            </div>

            <div>
              <label htmlFor="confirmPassword" className="block text-sm font-medium text-earth-700 mb-2">
                Confirm New Password
              </label>
              <Input
                id="confirmPassword"
                type="password"
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
                placeholder="Confirm new password"
                required
              />
            </div>

            <Button
              type="submit"
              variant="primary"
              size="lg"
              className="w-full"
              isLoading={isLoading}
            >
              {isLoading ? 'Updating Password...' : 'Update Password'}
            </Button>
          </form>
        </div>
      </div>
    </div>
  )
}

export default function ResetPasswordPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-earth-100 to-earth-200 px-4">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-accent-600 mx-auto"></div>
          <p className="mt-4 text-earth-600">Loading...</p>
        </div>
      </div>
    }>
      <ResetPasswordForm />
    </Suspense>
  )
}