import Link from 'next/link'
import { Button } from '@/components/ui/Button'

export default function VerifyEmailPage() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-earth-100 to-earth-200 px-4">
      <div className="max-w-md w-full space-y-8">
        <div className="text-center">
          <div className="mx-auto w-16 h-16 bg-accent-100 rounded-full flex items-center justify-center mb-6">
            <svg className="w-8 h-8 text-accent-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
            </svg>
          </div>
          <h1 className="text-3xl font-bold text-earth-900 mb-2">Check Your Email</h1>
          <p className="text-earth-600 mb-6">
            We&rsquo;ve sent you a verification link. Please check your email and click the link to activate your account.
          </p>
        </div>

        <div className="bg-white rounded-lg shadow-lg p-8 text-center">
          <p className="text-earth-700 mb-6">
            Didn&rsquo;t receive the email? Check your spam folder or request a new verification email.
          </p>
          
          <div className="space-y-4">
            <Button variant="primary" size="lg" className="w-full">
              Resend Verification Email
            </Button>
            
            <Link href="/auth/login">
              <Button variant="outline" size="lg" className="w-full">
                Back to Login
              </Button>
            </Link>
          </div>
        </div>
      </div>
    </div>
  )
}