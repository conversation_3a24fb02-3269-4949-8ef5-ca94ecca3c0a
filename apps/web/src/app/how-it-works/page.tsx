import { Search, Calendar, MessageCircle, Star } from 'lucide-react'
import { Card, CardContent } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import Link from 'next/link'

const steps = [
  {
    icon: Search,
    title: 'Browse & Search',
    description: 'Explore our curated selection of certified game farms and safari operators. Use our advanced filters to find experiences that match your preferences for location, activity type, and budget.',
    details: [
      'Search by location, activity type, or specific game species',
      'View detailed farm profiles with photos and amenities',
      'Read reviews from previous guests',
      'Compare prices and packages'
    ]
  },
  {
    icon: Calendar,
    title: 'Book Your Experience',
    description: 'Select your preferred dates and submit a booking request. Farm owners will review your request and confirm availability, providing you with detailed information about your upcoming safari.',
    details: [
      'Choose your preferred dates and group size',
      'Select hunting or photo safari activities',
      'Add special requests or requirements',
      'Receive confirmation with detailed itinerary'
    ]
  },
  {
    icon: MessageCircle,
    title: 'Connect & Communicate',
    description: 'Once your booking is confirmed, communicate directly with your farm operator to finalize details, ask questions, and ensure everything is prepared for your arrival.',
    details: [
      'Direct messaging with farm operators',
      'Receive pre-arrival information and instructions',
      'Coordinate transportation and logistics',
      'Get answers to any questions'
    ]
  },
  {
    icon: Star,
    title: 'Enjoy & Review',
    description: 'Experience your safari adventure and share your feedback. Your reviews help other travelers make informed decisions and help us maintain our high standards.',
    details: [
      'Enjoy your professionally guided safari experience',
      'Follow all safety guidelines and regulations',
      'Capture memories and create lasting experiences',
      'Leave a review to help future travelers'
    ]
  }
]

const userTypes = [
  {
    title: 'For Safari Enthusiasts',
    description: 'Whether you\'re interested in hunting or photography, find the perfect safari experience.',
    features: [
      'Access to verified game farms',
      'Detailed activity descriptions',
      'Transparent pricing',
      'Secure booking process',
      'Direct communication with operators'
    ]
  },
  {
    title: 'For Farm Owners',
    description: 'Showcase your farm and connect with qualified guests from around the world.',
    features: [
      'Professional farm profile creation',
      'Booking management tools',
      'Guest communication platform',
      'Review and rating system',
      'Marketing exposure'
    ]
  }
]

export default function HowItWorksPage() {
  return (
    <div className="min-h-screen bg-earth-100">
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-earth-600 to-earth-700 text-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-4xl md:text-5xl font-bold mb-6">How RvB Safaris Works</h1>
          <p className="text-xl md:text-2xl opacity-90 max-w-3xl mx-auto">
            Connecting safari enthusiasts with authentic African experiences in four simple steps.
          </p>
        </div>
      </section>

      {/* Steps Section */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="space-y-16">
            {steps.map((step, index) => (
              <div key={index} className="flex flex-col lg:flex-row items-center gap-8">
                <div className={`lg:w-1/2 ${index % 2 === 1 ? 'lg:order-2' : ''}`}>
                  <div className="flex items-center mb-6">
                    <div className="w-16 h-16 bg-accent-600 rounded-full flex items-center justify-center mr-4">
                      <step.icon className="w-8 h-8 text-white" />
                    </div>
                    <div>
                      <span className="text-accent-600 font-semibold">Step {index + 1}</span>
                      <h2 className="text-3xl font-bold text-earth-900">{step.title}</h2>
                    </div>
                  </div>
                  <p className="text-lg text-earth-700 mb-6">{step.description}</p>
                  <ul className="space-y-2">
                    {step.details.map((detail, detailIndex) => (
                      <li key={detailIndex} className="flex items-start">
                        <div className="w-2 h-2 bg-accent-600 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                        <span className="text-earth-700">{detail}</span>
                      </li>
                    ))}
                  </ul>
                </div>
                <div className={`lg:w-1/2 ${index % 2 === 1 ? 'lg:order-1' : ''}`}>
                  <Card className="h-64 bg-gradient-to-br from-earth-200 to-earth-300 flex items-center justify-center">
                    <CardContent className="text-center">
                      <step.icon className="w-24 h-24 text-earth-600 mx-auto mb-4" />
                      <p className="text-earth-700 font-medium">{step.title}</p>
                    </CardContent>
                  </Card>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* User Types Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold mb-4 text-earth-900">Built for Everyone</h2>
            <p className="text-lg text-earth-700 max-w-2xl mx-auto">
              Whether you&apos;re seeking adventure or sharing your farm with the world, RvB Safaris has you covered.
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {userTypes.map((userType, index) => (
              <Card key={index} className="p-8">
                <CardContent>
                  <h3 className="text-2xl font-bold mb-4 text-earth-900">{userType.title}</h3>
                  <p className="text-earth-700 mb-6">{userType.description}</p>
                  <ul className="space-y-3">
                    {userType.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-center">
                        <div className="w-2 h-2 bg-accent-600 rounded-full mr-3"></div>
                        <span className="text-earth-700">{feature}</span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-earth-600 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-6">Ready to Start Your Safari Adventure?</h2>
          <p className="text-xl opacity-90 mb-8 max-w-2xl mx-auto">
            Join thousands of satisfied travelers who have discovered their perfect safari experience through RvB Safaris.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/farms">
              <Button variant="secondary" size="lg">
                Browse Farms
              </Button>
            </Link>
            <Link href="/auth/register">
              <Button variant="outline" size="lg" className="border-white text-white hover:bg-white hover:text-earth-600">
                Create Account
              </Button>
            </Link>
          </div>
        </div>
      </section>
    </div>
  )
}
