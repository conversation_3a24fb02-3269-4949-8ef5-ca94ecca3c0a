"use client"

import Link from 'next/link'
import { useEffect, useState, useCallback } from 'react'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'
import { FarmCard } from '@/components/features/FarmCard'
import { HeroImageLoader } from '@/components/ui/HeroImageLoader'
import { farmService } from '@/lib/firebase/firestore'
import { GameFarm } from '@/lib/types/firestore'

interface FarmWithStats extends GameFarm {
  rating?: number
  reviewCount?: number
  activities: ('hunting' | 'photo_safari')[]
  imageUrl?: string
}

export default function Home() {
  const [featuredFarms, setFeaturedFarms] = useState<FarmWithStats[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const fetchFeaturedFarms = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)

      const farmsData = await farmService.getAll({
        featured: true,
        isActive: true,
        limit: 3
      })

      const processedFarms: FarmWithStats[] = farmsData.map((farm) => {
        // Convert activityTypes to activities array
        const activities: ('hunting' | 'photo_safari')[] = []
        if (farm.activityTypes === 'hunting' || farm.activityTypes === 'both') {
          activities.push('hunting')
        }
        if (farm.activityTypes === 'photo_safari' || farm.activityTypes === 'both') {
          activities.push('photo_safari')
        }

        return {
          ...farm,
          activities,
          rating: undefined, // TODO: Calculate from reviews subcollection
          reviewCount: 0, // TODO: Count from reviews subcollection
          imageUrl: '/globe.svg' // TODO: Get from images subcollection
        }
      })

      setFeaturedFarms(processedFarms)
    } catch (err) {
      console.error('Error fetching featured farms:', err)
      setError('Failed to load featured farms')
    } finally {
      setLoading(false)
    }
  }, [])

  useEffect(() => {
    fetchFeaturedFarms()
  }, [fetchFeaturedFarms])

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative text-white h-screen min-h-[600px]">
        <HeroImageLoader priority className="absolute inset-0">
          <div className="relative z-20 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 h-full flex items-center">
          <div className="text-center">
            <h1 className="text-4xl md:text-6xl font-bold mb-6 text-white font-[var(--font-display)]">
              Adventure Awaits in the African Wilderness
            </h1>
            <p className="text-xl md:text-2xl mb-8 opacity-90 max-w-3xl mx-auto">
              Discover South Africa&rsquo;s premier hunting and photo safari experiences.
              Connect with certified game farms and create unforgettable memories.
            </p>

            {/* Search Bar */}
            <div className="max-w-2xl mx-auto mb-8">
              <div className="relative">
                <Input
                  placeholder="Search farms, locations, or activities..."
                  className="pl-4 pr-16 py-4 text-lg border-0 focus:ring-4 focus:ring-white/20"
                />
                <button className="absolute right-2 top-1/2 transform -translate-y-1/2 bg-[var(--primary-brown)] text-white px-4 py-2 rounded-[var(--radius-md)] hover:bg-[#A0522D] transition-colors">
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                </button>
              </div>
            </div>

            {/* Activity Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <Link href="/farms?activity=hunting">
                <Button variant="hunting" size="lg" className="w-full sm:w-auto">
                  Book Hunting Safari
                </Button>
              </Link>
              <Link href="/farms?activity=photo_safari">
                <Button variant="photo" size="lg" className="w-full sm:w-auto">
                  Book Photo Safari
                </Button>
              </Link>
              <Link href="/farms">
                <Button variant="outline" size="lg" className="w-full sm:w-auto border-white text-white hover:bg-white hover:text-[var(--primary-brown)]">
                  View All Farms
                </Button>
              </Link>
            </div>
          </div>
          </div>
        </HeroImageLoader>
      </section>

      {/* Filter Pills */}
      <section className="py-8 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-wrap gap-3 justify-center">
            <div className="px-4 py-2 bg-[var(--primary-brown)] text-white rounded-full text-sm font-medium cursor-pointer">
              All
            </div>
            <div className="px-4 py-2 bg-white border-2 border-[var(--medium-gray)] text-[var(--dark-gray)] rounded-full text-sm font-medium cursor-pointer hover:border-[var(--primary-brown)] hover:bg-[var(--primary-brown)] hover:text-white transition-all">
              Big 5
            </div>
            <div className="px-4 py-2 bg-white border-2 border-[var(--medium-gray)] text-[var(--dark-gray)] rounded-full text-sm font-medium cursor-pointer hover:border-[var(--primary-brown)] hover:bg-[var(--primary-brown)] hover:text-white transition-all">
              Bow Hunting
            </div>
            <div className="px-4 py-2 bg-white border-2 border-[var(--medium-gray)] text-[var(--dark-gray)] rounded-full text-sm font-medium cursor-pointer hover:border-[var(--primary-brown)] hover:bg-[var(--primary-brown)] hover:text-white transition-all">
              Luxury Lodge
            </div>
            <div className="px-4 py-2 bg-white border-2 border-[var(--medium-gray)] text-[var(--dark-gray)] rounded-full text-sm font-medium cursor-pointer hover:border-[var(--primary-brown)] hover:bg-[var(--primary-brown)] hover:text-white transition-all">
              Eastern Cape
            </div>
            <div className="px-4 py-2 bg-white border-2 border-[var(--medium-gray)] text-[var(--dark-gray)] rounded-full text-sm font-medium cursor-pointer hover:border-[var(--primary-brown)] hover:bg-[var(--primary-brown)] hover:text-white transition-all">
              Limpopo
            </div>
          </div>
        </div>
      </section>

      {/* Featured Farms - Only show if there are featured farms */}
      {featuredFarms.length > 0 && (
        <section className="py-16 bg-[var(--light-gray)]">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold mb-4">Featured Game Farms</h2>
              <p className="text-lg text-[var(--medium-gray)] max-w-2xl mx-auto">
                Discover our handpicked selection of premium game farms offering exceptional hunting and photo safari experiences.
              </p>
            </div>

            {loading ? (
              <div className="text-center py-16">
                <p className="text-[var(--medium-gray)] text-lg">Loading featured farms...</p>
              </div>
            ) : error ? (
              <div className="text-center py-16">
                <p className="text-red-600 text-lg">{error}</p>
                <button
                  onClick={fetchFeaturedFarms}
                  className="mt-4 px-4 py-2 bg-[var(--primary-brown)] text-white rounded-lg hover:bg-[#A0522D]"
                >
                  Try Again
                </button>
              </div>
            ) : (
              <>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                  {featuredFarms.map((farm) => (
                    <FarmCard
                      key={farm.id}
                      id={farm.id}
                      name={farm.name}
                      location={farm.location}
                      province={farm.province}
                      description={farm.description || ''}
                      imageUrl={farm.imageUrl}
                      activities={farm.activities}
                      priceRange={farm.pricingInfo || 'Contact for pricing'}
                      rating={farm.rating}
                      reviewCount={farm.reviewCount}
                    />
                  ))}
                </div>

                <div className="text-center mt-12">
                  <Link href="/farms">
                    <Button variant="primary" size="lg">
                      View All Farms
                    </Button>
                  </Link>
                </div>
              </>
            )}
          </div>
        </section>
      )}

      {/* How It Works */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">How RvB Safaris Works</h2>
            <p className="text-lg text-[var(--medium-gray)] max-w-2xl mx-auto">
              Booking your perfect safari experience is simple and secure.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-[var(--primary-brown)] text-white rounded-full flex items-center justify-center text-2xl font-bold mx-auto mb-4">
                1
              </div>
              <h3 className="text-xl font-bold mb-2">Search & Discover</h3>
              <p className="text-[var(--medium-gray)]">
                Browse our curated selection of game farms, filter by activity type, location, and amenities.
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-[var(--primary-brown)] text-white rounded-full flex items-center justify-center text-2xl font-bold mx-auto mb-4">
                2
              </div>
              <h3 className="text-xl font-bold mb-2">Book & Connect</h3>
              <p className="text-[var(--medium-gray)]">
                Submit your booking request directly to the farm owner. Communicate your preferences and special requirements.
              </p>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-[var(--primary-brown)] text-white rounded-full flex items-center justify-center text-2xl font-bold mx-auto mb-4">
                3
              </div>
              <h3 className="text-xl font-bold mb-2">Experience & Enjoy</h3>
              <p className="text-[var(--medium-gray)]">
                Arrive at your chosen farm and enjoy an unforgettable safari experience with professional guides.
              </p>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}
