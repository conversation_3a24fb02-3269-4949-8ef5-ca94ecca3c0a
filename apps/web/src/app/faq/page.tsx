'use client'

import { useState } from 'react'
import { ChevronDown, ChevronUp } from 'lucide-react'

interface FAQItem {
  question: string
  answer: string
  category: 'general' | 'booking' | 'hunting' | 'photo' | 'safety'
}

const faqData: FAQItem[] = [
  {
    category: 'general',
    question: 'What is RvB Safaris?',
    answer: 'RvB Safaris is South Africa\'s premier platform connecting hunters, photographers, and safari enthusiasts with certified game farms and safari operators. We facilitate bookings and provide comprehensive information about safari experiences.'
  },
  {
    category: 'general',
    question: 'How do I create an account?',
    answer: 'Click the "Sign Up" button in the top right corner, choose your account type (Hunter/Guest or Farm Owner), and fill in your details. You\'ll receive a verification email to activate your account.'
  },
  {
    category: 'booking',
    question: 'How do I book a safari?',
    answer: 'Browse our farm listings, select your preferred farm and dates, choose your activity type (hunting or photo safari), and submit a booking request. The farm owner will review and confirm your booking.'
  },
  {
    category: 'booking',
    question: 'What payment methods are accepted?',
    answer: 'Payment methods vary by farm operator. Most accept bank transfers, credit cards, and cash payments. Specific payment terms will be provided when your booking is confirmed.'
  },
  {
    category: 'booking',
    question: 'Can I cancel my booking?',
    answer: 'Cancellation policies vary by farm operator and are specified in your booking confirmation. Generally, cancellations made well in advance may receive partial or full refunds, while last-minute cancellations may incur penalties.'
  },
  {
    category: 'hunting',
    question: 'Do I need a hunting license?',
    answer: 'Yes, you must have a valid hunting license and any required permits. International hunters need temporary hunting licenses issued by South African authorities. Farm operators can assist with the application process.'
  },
  {
    category: 'hunting',
    question: 'What hunting equipment is provided?',
    answer: 'Equipment provision varies by farm. Some provide rifles and ammunition for rent, while others require you to bring your own or arrange through licensed dealers. Check with your chosen farm for specific details.'
  },
  {
    category: 'hunting',
    question: 'Can I bring my own rifle?',
    answer: 'Yes, but you must follow South African firearm import regulations. This includes obtaining temporary import permits and declaring firearms at customs. We recommend consulting with your farm operator for guidance.'
  },
  {
    category: 'photo',
    question: 'What should I bring for a photo safari?',
    answer: 'Bring your camera equipment, extra batteries, memory cards, and appropriate clothing. Many farms provide vehicles and guides. Check with your chosen farm for specific recommendations and any equipment they provide.'
  },
  {
    category: 'photo',
    question: 'Are there restrictions on photography?',
    answer: 'Photography policies vary by farm. Some areas may be off-limits, and there may be restrictions on photographing certain activities or facilities. Always ask permission and respect the farm\'s guidelines.'
  },
  {
    category: 'safety',
    question: 'How safe are safari activities?',
    answer: 'Safety is our top priority. All listed farms maintain high safety standards and provide experienced guides. However, safari activities involve inherent risks. We strongly recommend comprehensive travel and activity insurance.'
  },
  {
    category: 'safety',
    question: 'What safety equipment is provided?',
    answer: 'Safety equipment varies by activity and farm. Hunting safaris typically include safety briefings and protective gear. Photo safaris provide safe vehicles and experienced guides. Specific safety measures are detailed in farm descriptions.'
  },
  {
    category: 'safety',
    question: 'Do I need travel insurance?',
    answer: 'Yes, we strongly recommend comprehensive travel and activity insurance that covers safari activities, medical emergencies, and equipment. Check that your policy covers hunting activities if applicable.'
  }
]

const categories = [
  { key: 'general', label: 'General' },
  { key: 'booking', label: 'Booking' },
  { key: 'hunting', label: 'Hunting' },
  { key: 'photo', label: 'Photo Safari' },
  { key: 'safety', label: 'Safety' }
]

export default function FAQPage() {
  const [selectedCategory, setSelectedCategory] = useState<string>('general')
  const [openItems, setOpenItems] = useState<Set<number>>(new Set())

  const toggleItem = (index: number) => {
    const newOpenItems = new Set(openItems)
    if (newOpenItems.has(index)) {
      newOpenItems.delete(index)
    } else {
      newOpenItems.add(index)
    }
    setOpenItems(newOpenItems)
  }

  const filteredFAQs = faqData.filter(item => item.category === selectedCategory)

  return (
    <div className="min-h-screen bg-earth-100 py-16">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h1 className="text-4xl md:text-5xl font-bold mb-6 text-earth-900">Frequently Asked Questions</h1>
          <p className="text-xl text-earth-700">
            Find answers to common questions about RvB Safaris and safari experiences.
          </p>
        </div>

        {/* Category Tabs */}
        <div className="flex flex-wrap justify-center mb-8 bg-white rounded-lg p-2 shadow-md">
          {categories.map((category) => (
            <button
              key={category.key}
              onClick={() => setSelectedCategory(category.key)}
              className={`px-4 py-2 mx-1 my-1 rounded-md font-medium transition-colors ${
                selectedCategory === category.key
                  ? 'bg-accent-600 text-white'
                  : 'text-earth-700 hover:bg-earth-100'
              }`}
            >
              {category.label}
            </button>
          ))}
        </div>

        {/* FAQ Items */}
        <div className="space-y-4">
          {filteredFAQs.map((item, index) => (
            <div key={index} className="bg-white rounded-lg shadow-md overflow-hidden">
              <button
                onClick={() => toggleItem(index)}
                className="w-full px-6 py-4 text-left flex justify-between items-center hover:bg-earth-50 transition-colors"
              >
                <h3 className="text-lg font-medium text-earth-900 pr-4">{item.question}</h3>
                {openItems.has(index) ? (
                  <ChevronUp className="w-5 h-5 text-earth-600 flex-shrink-0" />
                ) : (
                  <ChevronDown className="w-5 h-5 text-earth-600 flex-shrink-0" />
                )}
              </button>
              {openItems.has(index) && (
                <div className="px-6 pb-4">
                  <p className="text-earth-700 leading-relaxed">{item.answer}</p>
                </div>
              )}
            </div>
          ))}
        </div>

        {/* Contact Section */}
        <div className="mt-12 bg-white rounded-lg shadow-md p-8 text-center">
          <h2 className="text-2xl font-bold mb-4 text-earth-900">Still Have Questions?</h2>
          <p className="text-earth-700 mb-6">
            Can&apos;t find what you&apos;re looking for? Our support team is here to help.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a
              href="/contact"
              className="inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-accent-600 hover:bg-accent-700 transition-colors"
            >
              Contact Support
            </a>
            <a
              href="mailto:<EMAIL>"
              className="inline-flex items-center justify-center px-6 py-3 border border-earth-300 text-base font-medium rounded-md text-earth-700 bg-white hover:bg-earth-50 transition-colors"
            >
              Email Us
            </a>
          </div>
        </div>
      </div>
    </div>
  )
}
