'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/hooks/useAuth'
import { Button } from '@/components/ui/Button'
import { Card, CardContent } from '@/components/ui/Card'
import { LoadingSpinner } from '@/components/ui/LoadingSpinner'
import { farmService } from '@/lib/firebase/firestore'
import { GameFarm } from '@/lib/types/firestore'
import { ArrowLeft, Plus, MapPin, Eye, Edit, BarChart3, Users, Star } from 'lucide-react'
import Link from 'next/link'

export default function FarmsPage() {
  const { user, userProfile, loading: authLoading } = useAuth()
  const router = useRouter()
  const [farms, setFarms] = useState<GameFarm[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchFarms = async () => {
      if (!userProfile?.id) return

      try {
        setLoading(true)
        setError(null)

        // Only farm owners should access this page
        if (userProfile.role !== 'farm_owner') {
          router.push('/dashboard')
          return
        }

        const userFarms = await farmService.getByOwner(userProfile.id)
        setFarms(userFarms)
      } catch (err) {
        console.error('Error fetching farms:', err)
        setError('Failed to load farms')
      } finally {
        setLoading(false)
      }
    }

    if (!authLoading && userProfile) {
      fetchFarms()
    }
  }, [authLoading, userProfile, router])

  useEffect(() => {
    if (!authLoading && !user) {
      router.push('/auth/login')
    }
  }, [authLoading, user, router])

  if (authLoading || loading) {
    return (
      <div className="min-h-screen bg-earth-100 flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  if (!user) {
    return null // Will redirect to login
  }

  // Redirect non-farm owners
  if (userProfile?.role !== 'farm_owner') {
    router.push('/dashboard')
    return null
  }

  return (
    <div className="min-h-screen bg-earth-100 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center gap-4 mb-4">
            <Link href="/dashboard">
              <Button variant="outline" size="sm">
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Dashboard
              </Button>
            </Link>
          </div>

          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-earth-900">My Farms</h1>
              <p className="text-earth-600 mt-2">
                Manage your farm listings and track their performance
              </p>
            </div>
            <Link href="/farms/create">
              <Button variant="primary" className="flex items-center gap-2">
                <Plus className="w-4 h-4" />
                Add New Farm
              </Button>
            </Link>
          </div>
        </div>

        {error && (
          <div className="mb-6 bg-red-50 border border-red-200 rounded-md p-4">
            <p className="text-red-600">{error}</p>
          </div>
        )}

        {/* Farms Grid */}
        {farms.length === 0 ? (
          <Card className="p-8">
            <div className="text-center">
              <MapPin className="w-16 h-16 text-earth-400 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-earth-900 mb-2">
                No farms yet
              </h3>
              <p className="text-earth-600 mb-6">
                Create your first farm listing to start receiving bookings from safari enthusiasts.
              </p>
              <Link href="/farms/create">
                <Button variant="primary" className="flex items-center gap-2">
                  <Plus className="w-4 h-4" />
                  Create Your First Farm
                </Button>
              </Link>
            </div>
          </Card>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {farms.map((farm) => (
              <Card key={farm.id} className="overflow-hidden">
                <CardContent className="p-0">
                  {/* Farm Image Placeholder */}
                  <div className="h-48 bg-gradient-to-br from-earth-200 to-earth-300 flex items-center justify-center">
                    <MapPin className="w-12 h-12 text-earth-600" />
                  </div>

                  <div className="p-6">
                    {/* Farm Header */}
                    <div className="flex items-start justify-between mb-3">
                      <div>
                        <h3 className="text-lg font-semibold text-earth-900 mb-1">
                          {farm.name}
                        </h3>
                        <p className="text-sm text-earth-600 flex items-center gap-1">
                          <MapPin className="w-3 h-3" />
                          {farm.location}, {farm.province}
                        </p>
                      </div>
                      <div className="flex items-center gap-1">
                        <div className={`w-2 h-2 rounded-full ${farm.isActive ? 'bg-green-500' : 'bg-red-500'}`} />
                        <span className="text-xs text-earth-600">
                          {farm.isActive ? 'Active' : 'Inactive'}
                        </span>
                      </div>
                    </div>

                    {/* Farm Details */}
                    <div className="space-y-2 mb-4">
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-earth-600">Activity Type:</span>
                        <span className="font-medium capitalize text-earth-900">
                          {farm.activityTypes.replace('_', ' ')}
                        </span>
                      </div>
                      {farm.sizeHectares && (
                        <div className="flex items-center justify-between text-sm">
                          <span className="text-earth-600">Size:</span>
                          <span className="font-medium text-earth-900">
                            {farm.sizeHectares.toLocaleString()} hectares
                          </span>
                        </div>
                      )}
                      {farm.pricePerDay && (
                        <div className="flex items-center justify-between text-sm">
                          <span className="text-earth-600">Price:</span>
                          <span className="font-medium text-earth-900">
                            R{farm.pricePerDay.toLocaleString()}/day
                          </span>
                        </div>
                      )}
                    </div>

                    {/* Action Buttons */}
                    <div className="grid grid-cols-2 gap-2">
                      <Link href={`/farms/${farm.id}`}>
                        <Button variant="outline" size="sm" className="w-full flex items-center gap-1">
                          <Eye className="w-3 h-3" />
                          View
                        </Button>
                      </Link>
                      <Link href={`/farms/${farm.id}/edit`}>
                        <Button variant="outline" size="sm" className="w-full flex items-center gap-1">
                          <Edit className="w-3 h-3" />
                          Edit
                        </Button>
                      </Link>
                    </div>

                    <div className="mt-2">
                      <Link href={`/farms/${farm.id}/analytics`}>
                        <Button variant="primary" size="sm" className="w-full flex items-center gap-1">
                          <BarChart3 className="w-3 h-3" />
                          Analytics
                        </Button>
                      </Link>
                    </div>

                    {/* Quick Stats */}
                    <div className="mt-4 pt-4 border-t border-earth-200">
                      <div className="grid grid-cols-2 gap-4 text-center">
                        <div>
                          <div className="flex items-center justify-center gap-1 text-earth-600 mb-1">
                            <Users className="w-3 h-3" />
                            <span className="text-xs">Bookings</span>
                          </div>
                          <p className="text-sm font-semibold text-earth-900">0</p>
                        </div>
                        <div>
                          <div className="flex items-center justify-center gap-1 text-earth-600 mb-1">
                            <Star className="w-3 h-3" />
                            <span className="text-xs">Rating</span>
                          </div>
                          <p className="text-sm font-semibold text-earth-900">N/A</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}

        {/* Summary Stats */}
        {farms.length > 0 && (
          <div className="mt-8 grid grid-cols-1 md:grid-cols-3 gap-6">
            <Card className="p-6">
              <div className="text-center">
                <p className="text-2xl font-bold text-earth-900">{farms.length}</p>
                <p className="text-earth-600 text-sm">Total Farms</p>
              </div>
            </Card>
            <Card className="p-6">
              <div className="text-center">
                <p className="text-2xl font-bold text-earth-900">
                  {farms.filter(f => f.isActive).length}
                </p>
                <p className="text-earth-600 text-sm">Active Listings</p>
              </div>
            </Card>
            <Card className="p-6">
              <div className="text-center">
                <p className="text-2xl font-bold text-earth-900">
                  {farms.filter(f => f.featured).length}
                </p>
                <p className="text-earth-600 text-sm">Featured Farms</p>
              </div>
            </Card>
          </div>
        )}
      </div>
    </div>
  )
}
