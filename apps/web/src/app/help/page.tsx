import { Search, Book, MessageCircle, Shield, FileText, Phone } from 'lucide-react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import Link from 'next/link'

const helpCategories = [
  {
    icon: Book,
    title: 'Getting Started',
    description: 'Learn the basics of using RvB Safaris',
    links: [
      { title: 'Creating Your Account', href: '/help/account-setup' },
      { title: 'How to Search for Farms', href: '/help/searching' },
      { title: 'Understanding Farm Profiles', href: '/help/farm-profiles' },
      { title: 'Platform Overview', href: '/how-it-works' }
    ]
  },
  {
    icon: Search,
    title: 'Booking & Reservations',
    description: 'Everything about making and managing bookings',
    links: [
      { title: 'How to Make a Booking', href: '/help/booking-process' },
      { title: 'Payment Methods', href: '/help/payments' },
      { title: 'Cancellation Policies', href: '/help/cancellations' },
      { title: 'Booking Confirmations', href: '/help/confirmations' }
    ]
  },
  {
    icon: MessageCircle,
    title: 'Communication',
    description: 'Connecting with farm operators and support',
    links: [
      { title: 'Messaging Farm Owners', href: '/help/messaging' },
      { title: 'Pre-Arrival Communication', href: '/help/pre-arrival' },
      { title: 'Emergency Contacts', href: '/help/emergency' },
      { title: 'Language Support', href: '/help/languages' }
    ]
  },
  {
    icon: Shield,
    title: 'Safety & Guidelines',
    description: 'Important safety information and guidelines',
    links: [
      { title: 'Safari Safety Guidelines', href: '/safety' },
      { title: 'Hunting Regulations', href: '/help/hunting-rules' },
      { title: 'Equipment Requirements', href: '/help/equipment' },
      { title: 'Insurance Recommendations', href: '/help/insurance' }
    ]
  },
  {
    icon: FileText,
    title: 'Legal & Policies',
    description: 'Terms, privacy, and legal information',
    links: [
      { title: 'Terms of Service', href: '/terms' },
      { title: 'Privacy Policy', href: '/privacy' },
      { title: 'POPIA Compliance', href: '/help/popia' },
      { title: 'Dispute Resolution', href: '/help/disputes' }
    ]
  },
  {
    icon: Phone,
    title: 'Contact & Support',
    description: 'Get in touch with our support team',
    links: [
      { title: 'Contact Information', href: '/contact' },
      { title: 'Support Hours', href: '/help/support-hours' },
      { title: 'Emergency Support', href: '/help/emergency-support' },
      { title: 'Feedback & Suggestions', href: '/help/feedback' }
    ]
  }
]

const quickActions = [
  {
    title: 'Track Your Booking',
    description: 'Check the status of your current bookings',
    action: 'Go to Dashboard',
    href: '/dashboard'
  },
  {
    title: 'Contact Support',
    description: 'Get help from our support team',
    action: 'Contact Us',
    href: '/contact'
  },
  {
    title: 'Browse FAQs',
    description: 'Find answers to common questions',
    action: 'View FAQs',
    href: '/faq'
  }
]

export default function HelpPage() {
  return (
    <div className="min-h-screen bg-earth-100">
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-earth-600 to-earth-700 text-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-4xl md:text-5xl font-bold mb-6">Help Center</h1>
          <p className="text-xl md:text-2xl opacity-90 max-w-3xl mx-auto mb-8">
            Find answers, get support, and learn how to make the most of your RvB Safaris experience.
          </p>
          
          {/* Search Bar */}
          <div className="max-w-2xl mx-auto">
            <div className="relative">
              <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-earth-400 w-5 h-5" />
              <input
                type="text"
                placeholder="Search for help articles..."
                className="w-full pl-12 pr-4 py-3 rounded-lg text-earth-900 placeholder-earth-500 focus:outline-none focus:ring-2 focus:ring-accent-600"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Quick Actions */}
      <section className="py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-2xl font-bold text-earth-900 mb-8 text-center">Quick Actions</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {quickActions.map((action, index) => (
              <Card key={index} className="text-center p-6 hover:shadow-lg transition-shadow">
                <CardContent>
                  <h3 className="text-lg font-semibold text-earth-900 mb-2">{action.title}</h3>
                  <p className="text-earth-700 mb-4">{action.description}</p>
                  <Link href={action.href}>
                    <Button variant="primary" size="sm">
                      {action.action}
                    </Button>
                  </Link>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Help Categories */}
      <section className="py-12 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-3xl font-bold text-earth-900 mb-12 text-center">Browse Help Topics</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {helpCategories.map((category, index) => (
              <Card key={index} className="h-full">
                <CardHeader>
                  <div className="flex items-center mb-4">
                    <div className="w-12 h-12 bg-accent-100 rounded-lg flex items-center justify-center mr-4">
                      <category.icon className="w-6 h-6 text-accent-600" />
                    </div>
                    <CardTitle className="text-xl">{category.title}</CardTitle>
                  </div>
                  <p className="text-earth-700">{category.description}</p>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-3">
                    {category.links.map((link, linkIndex) => (
                      <li key={linkIndex}>
                        <Link 
                          href={link.href}
                          className="text-accent-600 hover:text-accent-700 hover:underline transition-colors"
                        >
                          {link.title}
                        </Link>
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Contact Support */}
      <section className="py-16 bg-earth-50">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold text-earth-900 mb-6">Still Need Help?</h2>
          <p className="text-lg text-earth-700 mb-8">
            Our support team is available to assist you with any questions or issues you may have.
          </p>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
            <Card className="p-6">
              <CardContent className="text-center">
                <MessageCircle className="w-12 h-12 text-accent-600 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-earth-900 mb-2">Live Chat</h3>
                <p className="text-earth-700 mb-4">Get instant help from our support team</p>
                <Button variant="primary">Start Chat</Button>
              </CardContent>
            </Card>
            
            <Card className="p-6">
              <CardContent className="text-center">
                <Phone className="w-12 h-12 text-accent-600 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-earth-900 mb-2">Phone Support</h3>
                <p className="text-earth-700 mb-4">Speak directly with our team</p>
                <Button variant="outline">Call Now</Button>
              </CardContent>
            </Card>
          </div>

          <div className="bg-white rounded-lg p-6 shadow-md">
            <h3 className="text-lg font-semibold text-earth-900 mb-4">Support Hours</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
              <div>
                <p className="font-medium text-earth-900">Monday - Friday</p>
                <p className="text-earth-700">8:00 AM - 6:00 PM SAST</p>
              </div>
              <div>
                <p className="font-medium text-earth-900">Saturday</p>
                <p className="text-earth-700">9:00 AM - 4:00 PM SAST</p>
              </div>
              <div>
                <p className="font-medium text-earth-900">Sunday</p>
                <p className="text-earth-700">Emergency Support Only</p>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}
