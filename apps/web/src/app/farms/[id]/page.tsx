'use client'

import { useState, useEffect, useCallback } from 'react'
import { useParams } from 'next/navigation'
import Image from 'next/image'
import { Button } from '@/components/ui/Button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import { Badge } from '@/components/ui/Badge'
import { BookingModal } from '@/components/features/BookingModal'
import { ReviewCard, ReviewSummary } from '@/components/features/ReviewCard'
import { getRandomHeroImageUrl, getDefaultHeroImageUrl } from '@/lib/firebase/heroImages'
import { farmService, reviewService } from '@/lib/firebase/firestore'
import { GameFarm, Review, FarmAmenity, ActivityType, FarmImage } from '@/lib/types/firestore'

interface FarmWithDetails extends GameFarm {
  activities: ActivityType[]
  amenities: FarmAmenity[]
  species: string[]
  reviews: Review[]
  images: FarmImage[]
  rating: number
}

export default function FarmDetailPage() {
  const params = useParams()
  const [farm, setFarm] = useState<FarmWithDetails | null>(null)
  const [selectedActivity, setSelectedActivity] = useState<ActivityType>('hunting')
  const [showBookingModal, setShowBookingModal] = useState(false)
  const [heroImg, setHeroImg] = useState<string>(getDefaultHeroImageUrl())
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchFarmDetails = useCallback(async (farmId: string) => {
    try {
      setLoading(true)
      setError(null)

      // Fetch farm data
      const farmData = await farmService.get(farmId)
      if (!farmData) throw new Error('Farm not found')

      // Fetch reviews for this farm
      const reviews = await reviewService.getByFarm(farmId)

      // Process the data
      const activities: ActivityType[] = []
      if (farmData.activityTypes === 'hunting') activities.push('hunting')
      else if (farmData.activityTypes === 'photo_safari') activities.push('photo_safari')
      else if (farmData.activityTypes === 'both') {
        activities.push('hunting', 'photo_safari')
      }

      // TODO: Fetch amenities, species, and images from subcollections
      const amenities: FarmAmenity[] = []
      const species: string[] = []
      const images: FarmImage[] = []

      const rating = reviews.length > 0
        ? reviews.reduce((sum: number, review: Review) => sum + review.rating, 0) / reviews.length
        : 0

      setFarm({
        ...farmData,
        activities,
        amenities,
        species,
        reviews,
        images,
        rating: Math.round(rating * 10) / 10
      })

      // Set default activity based on farm's offerings
      if (activities.length > 0) {
        setSelectedActivity(activities[0])
      }

    } catch (err) {
      console.error('Error fetching farm details:', err)
      setError(err instanceof Error ? err.message : 'Failed to load farm details')
    } finally {
      setLoading(false)
    }
  }, [])

  useEffect(() => {
    getRandomHeroImageUrl().then(setHeroImg)
    if (params.id) {
      fetchFarmDetails(params.id as string)
    }
  }, [params.id, fetchFarmDetails])

  if (loading) {
    return (
      <div className="min-h-screen bg-earth-100 flex items-center justify-center">
        <p className="text-earth-600 text-lg">Loading farm details...</p>
      </div>
    )
  }

  if (error || !farm) {
    return (
      <div className="min-h-screen bg-earth-100 flex items-center justify-center">
        <div className="text-center">
          <p className="text-red-600 text-lg mb-4">{error || 'Farm not found'}</p>
          <Button onClick={() => window.history.back()} variant="outline">
            Go Back
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-earth-100">
      {/* Hero Section */}
      <div className="relative">
        <div
          className="absolute inset-0 z-0 h-96"
          style={{
            backgroundImage: `url(${heroImg})`,
            backgroundSize: 'cover',
            backgroundPosition: 'center',
            filter: 'brightness(0.55)',
          }}
          aria-hidden="true"
        />
        {/* Overlay gradient for readability */}
        <div className="absolute inset-0 z-10 h-96 bg-gradient-to-br from-earth-600/30 to-earth-900/10" />
        <div className="relative z-20 h-96 flex items-center justify-center">
          <div className="text-center text-white">
            <h1 className="text-4xl md:text-5xl font-bold mb-4">{farm.name}</h1>
            <p className="text-xl">📍 {farm.location}, {farm.province}</p>
            <div className="flex items-center justify-center mt-4">
              <span className="text-accent-600 mr-2 text-2xl">★</span>
              <span className="text-xl">{farm.rating || 'New'}</span>
              <span className="ml-2">({farm.reviews.length} reviews)</span>
            </div>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-8">
            {/* Image Gallery */}
            <Card>
              <CardContent className="p-0">
                {farm.images.length > 0 ? (
                  <div className="space-y-4">
                    {/* Main Image */}
                    <div className="relative h-64 md:h-80">
                      <Image
                        src={farm.images[0].imageUrl}
                        alt={farm.images[0].altText || `${farm.name} main image`}
                        fill
                        className="object-cover"
                        sizes="(max-width: 768px) 100vw, (max-width: 1200px) 66vw, 50vw"
                      />
                      {farm.images.length > 1 && (
                        <div className="absolute bottom-4 right-4 bg-black bg-opacity-50 text-white px-3 py-1 rounded-full text-sm">
                          1 / {farm.images.length}
                        </div>
                      )}
                    </div>

                    {/* Thumbnail Gallery */}
                    {farm.images.length > 1 && (
                      <div className="p-4">
                        <div className="grid grid-cols-4 md:grid-cols-6 gap-2">
                          {farm.images.slice(1, 7).map((image, index) => (
                            <div key={image.id} className="relative aspect-square">
                              <Image
                                src={image.imageUrl}
                                alt={image.altText || `${farm.name} image ${index + 2}`}
                                fill
                                className="object-cover rounded cursor-pointer hover:opacity-80 transition-opacity"
                                sizes="(max-width: 768px) 25vw, (max-width: 1200px) 16vw, 12vw"
                              />
                            </div>
                          ))}
                          {farm.images.length > 7 && (
                            <div className="relative aspect-square bg-earth-200 rounded flex items-center justify-center cursor-pointer hover:bg-earth-300 transition-colors">
                              <span className="text-earth-600 text-sm font-medium">
                                +{farm.images.length - 7}
                              </span>
                            </div>
                          )}
                        </div>
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="h-64 bg-gradient-to-br from-earth-600 to-earth-900 flex items-center justify-center text-white">
                    <div className="text-center">
                      <div className="text-6xl mb-4">🏞️</div>
                      <p className="text-lg">No images available</p>
                      <p className="text-sm opacity-75">Contact farm for photos</p>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Activities */}
            <Card>
              <CardHeader>
                <CardTitle>Available Activities</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex gap-4 mb-6">
                  {farm.activities.map((activity) => (
                    <button
                      key={activity}
                      onClick={() => setSelectedActivity(activity)}
                      className={`px-6 py-3 rounded-lg font-semibold transition-colors ${
                        selectedActivity === activity
                          ? activity === 'hunting'
                            ? 'bg-hunting-accent text-white'
                            : 'bg-photo-accent text-white'
                          : 'bg-earth-200 text-earth-700 hover:bg-earth-300'
                      }`}
                    >
                      {activity === 'hunting' ? 'Hunting Safari' : 'Photo Safari'}
                    </button>
                  ))}
                </div>

                <div className="bg-earth-50 p-4 rounded-lg">
                  <h4 className="font-semibold text-earth-900 mb-2">
                    {selectedActivity === 'hunting' ? 'Hunting Safari' : selectedActivity === 'photo_safari' ? 'Photo Safari' : 'Combined Safari'} Experience
                  </h4>
                  <p className="text-earth-700">
                    {selectedActivity === 'hunting'
                      ? 'Professional guided hunting with experienced trackers. All equipment provided. Trophy preparation available.'
                      : selectedActivity === 'photo_safari'
                      ? 'Guided photography tours with professional wildlife photographers. Best spots for wildlife viewing and photography.'
                      : 'Experience both hunting and photography opportunities with our expert guides.'}
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* Description */}
            <Card>
              <CardHeader>
                <CardTitle>About {farm.name}</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-earth-700 leading-relaxed">{farm.description}</p>
              </CardContent>
            </Card>

            {/* Species & Amenities */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Available Species</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex flex-wrap gap-2">
                    {farm.species.length > 0 ? farm.species.map((species) => (
                      <Badge key={species} variant="default">
                        {species}
                      </Badge>
                    )) : (
                      <p className="text-earth-600 text-sm">Species information coming soon</p>
                    )}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Amenities</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex flex-wrap gap-2">
                    {farm.amenities.length > 0 ? farm.amenities.map((amenity) => (
                      <Badge key={amenity.id} variant="location">
                        {amenity.name}
                      </Badge>
                    )) : (
                      <p className="text-earth-600 text-sm">Amenities information coming soon</p>
                    )}
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Reviews */}
            <div className="space-y-6">
              <h2 className="text-2xl font-bold text-earth-900">Guest Reviews</h2>

              <ReviewSummary reviews={farm.reviews} />

              <div className="space-y-4">
                {farm.reviews.length > 0 ? (
                  farm.reviews.slice(0, 5).map((review) => (
                    <ReviewCard key={review.id} review={review} />
                  ))
                ) : (
                  <Card>
                    <CardContent className="text-center py-12">
                      <div className="text-6xl mb-4">⭐</div>
                      <h3 className="text-lg font-semibold text-earth-900 mb-2">No reviews yet</h3>
                      <p className="text-earth-600">Be the first to review this farm after your visit!</p>
                    </CardContent>
                  </Card>
                )}

                {farm.reviews.length > 5 && (
                  <div className="text-center">
                    <Button variant="outline">
                      View All {farm.reviews.length} Reviews
                    </Button>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Booking Card */}
            <Card className="sticky top-24">
              <CardHeader>
                <CardTitle>Book Your Safari</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="mb-4">
                  <p className="text-2xl font-bold text-earth-900">{farm.pricingInfo || 'Contact for pricing'}</p>
                  <p className="text-earth-600 text-sm">per person per day</p>
                </div>

                <Button
                  variant="primary"
                  size="lg"
                  className="w-full mb-4"
                  onClick={() => setShowBookingModal(true)}
                >
                  Request Booking
                </Button>

                <div className="space-y-2 text-sm text-earth-700">
                  <p>• Minimum 3 days for hunting</p>
                  <p>• Professional guide included</p>
                  <p>• Accommodation available</p>
                </div>
              </CardContent>
            </Card>

            {/* Contact Info */}
            <Card>
              <CardHeader>
                <CardTitle>Contact Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div>
                  <p className="font-medium text-earth-900">Phone</p>
                  <p className="text-earth-700">{farm.contactPhone || 'Contact via email'}</p>
                </div>
                <div>
                  <p className="font-medium text-earth-900">Email</p>
                  <p className="text-earth-700">{farm.contactEmail}</p>
                </div>
                {farm.websiteUrl && (
                  <div>
                    <p className="font-medium text-earth-900">Website</p>
                    <p className="text-earth-700">{farm.websiteUrl}</p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Farm Rules */}
            <Card>
              <CardHeader>
                <CardTitle>Important Information</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-sm text-earth-700">
                  {farm.rules ? (
                    <div dangerouslySetInnerHTML={{ __html: farm.rules.replace(/\n/g, '<br>') }} />
                  ) : (
                    <p>Please contact the farm for specific rules and requirements.</p>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      {/* Booking Modal */}
      {showBookingModal && (
        <BookingModal
          farm={farm}
          activity={selectedActivity}
          onClose={() => setShowBookingModal(false)}
        />
      )}
    </div>
  )
}