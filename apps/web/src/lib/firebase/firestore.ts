import {
  collection,
  doc,
  getDoc,
  getDocs,
  addDoc,
  setDoc,
  updateDoc,
  deleteDoc,
  query,
  where,
  orderBy,
  limit,
  Timestamp,
  DocumentSnapshot,
  QueryDocumentSnapshot,
  DocumentData,
  Query
} from 'firebase/firestore'
import { db } from './client'
import {
  UserProfile,
  GameFarm,
  Booking,
  Review,
  FarmImage,
  GameSpecies,
  FarmAmenity,
  FirestoreDocument
} from '@/lib/types/firestore'

// Helper function to convert Firestore document to typed object
export function docToData<T extends FirestoreDocument>(
  doc: QueryDocumentSnapshot<DocumentData> | DocumentSnapshot<DocumentData>
): T | null {
  if (!doc.exists()) return null
  
  const data = doc.data()
  return {
    id: doc.id,
    ...data,
    // Convert Firestore Timestamps to Date objects for easier handling
    createdAt: data?.createdAt?.toDate?.() || data?.createdAt,
    updatedAt: data?.updatedAt?.toDate?.() || data?.updatedAt,
  } as T
}



// User Profile operations
export const userProfileService = {
  async get(userId: string): Promise<UserProfile | null> {
    const docRef = doc(db, 'users', userId)
    const docSnap = await getDoc(docRef)
    return docToData<UserProfile>(docSnap)
  },

  async create(userId: string, data: Omit<UserProfile, 'id' | 'createdAt' | 'updatedAt'>): Promise<void> {
    const docRef = doc(db, 'users', userId)
    const now = new Date()
    await setDoc(docRef, {
      ...data,
      createdAt: Timestamp.fromDate(now),
      updatedAt: Timestamp.fromDate(now)
    })
  },

  async update(userId: string, data: Partial<UserProfile>): Promise<void> {
    const docRef = doc(db, 'users', userId)
    await updateDoc(docRef, {
      ...data,
      updatedAt: Timestamp.now()
    })
  }
}

// Game Farm operations
export const farmService = {
  async getAll(filters?: {
    isActive?: boolean
    featured?: boolean
    province?: string
    activityType?: string
    limit?: number
  }): Promise<GameFarm[]> {
    let q: Query<DocumentData> = collection(db, 'farms')

    if (filters?.isActive !== undefined) {
      q = query(q, where('isActive', '==', filters.isActive))
    }
    if (filters?.featured !== undefined) {
      q = query(q, where('featured', '==', filters.featured))
    }
    if (filters?.province) {
      q = query(q, where('province', '==', filters.province))
    }
    if (filters?.activityType) {
      q = query(q, where('activityTypes', '==', filters.activityType))
    }

    q = query(q, orderBy('createdAt', 'desc'))

    if (filters?.limit) {
      q = query(q, limit(filters.limit))
    }

    const querySnapshot = await getDocs(q)
    return querySnapshot.docs.map(doc => docToData<GameFarm>(doc)!).filter(Boolean)
  },

  async get(farmId: string): Promise<GameFarm | null> {
    const docRef = doc(db, 'farms', farmId)
    const docSnap = await getDoc(docRef)
    return docToData<GameFarm>(docSnap)
  },

  async getByOwner(ownerId: string): Promise<GameFarm[]> {
    const q = query(
      collection(db, 'farms'),
      where('ownerId', '==', ownerId),
      orderBy('createdAt', 'desc')
    )
    const querySnapshot = await getDocs(q)
    return querySnapshot.docs.map(doc => docToData<GameFarm>(doc)!).filter(Boolean)
  },

  async getActive(limitCount?: number): Promise<GameFarm[]> {
    let q = query(
      collection(db, 'farms'),
      where('isActive', '==', true),
      orderBy('createdAt', 'desc')
    )

    if (limitCount) {
      q = query(q, limit(limitCount))
    }

    const querySnapshot = await getDocs(q)
    return querySnapshot.docs.map(doc => docToData<GameFarm>(doc)!).filter(Boolean)
  },

  async create(data: Omit<GameFarm, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {
    const now = new Date()
    const docRef = await addDoc(collection(db, 'farms'), {
      ...data,
      createdAt: Timestamp.fromDate(now),
      updatedAt: Timestamp.fromDate(now)
    })
    return docRef.id
  },

  async update(farmId: string, data: Partial<GameFarm>): Promise<void> {
    const docRef = doc(db, 'farms', farmId)
    await updateDoc(docRef, {
      ...data,
      updatedAt: Timestamp.now()
    })
  },

  async delete(farmId: string): Promise<void> {
    const docRef = doc(db, 'farms', farmId)
    await deleteDoc(docRef)
  },

  // Add farm images to subcollection
  async addImage(farmId: string, imageData: Omit<FarmImage, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {
    const now = new Date()
    const docRef = await addDoc(collection(db, 'farms', farmId, 'images'), {
      ...imageData,
      createdAt: Timestamp.fromDate(now),
      updatedAt: Timestamp.fromDate(now)
    })
    return docRef.id
  },

  // Get farm images
  async getImages(farmId: string): Promise<FarmImage[]> {
    const q = query(
      collection(db, 'farms', farmId, 'images'),
      orderBy('displayOrder', 'asc'),
      orderBy('createdAt', 'asc')
    )
    const querySnapshot = await getDocs(q)
    return querySnapshot.docs.map(doc => docToData<FarmImage>(doc)!).filter(Boolean)
  },

  // Delete farm image
  async deleteImage(farmId: string, imageId: string): Promise<void> {
    const docRef = doc(db, 'farms', farmId, 'images', imageId)
    await deleteDoc(docRef)
  }
}

// Booking operations
export const bookingService = {
  async getAll(filters?: {
    hunterId?: string
    farmId?: string
    status?: string
    limit?: number
  }): Promise<Booking[]> {
    let q: Query<DocumentData> = collection(db, 'bookings')

    if (filters?.hunterId) {
      q = query(q, where('hunterId', '==', filters.hunterId))
    }
    if (filters?.farmId) {
      q = query(q, where('farmId', '==', filters.farmId))
    }
    if (filters?.status) {
      q = query(q, where('status', '==', filters.status))
    }

    q = query(q, orderBy('createdAt', 'desc'))

    if (filters?.limit) {
      q = query(q, limit(filters.limit))
    }

    const querySnapshot = await getDocs(q)
    return querySnapshot.docs.map(doc => docToData<Booking>(doc)!).filter(Boolean)
  },

  async get(bookingId: string): Promise<Booking | null> {
    const docRef = doc(db, 'bookings', bookingId)
    const docSnap = await getDoc(docRef)
    return docToData<Booking>(docSnap)
  },

  async create(data: Omit<Booking, 'id' | 'createdAt' | 'updatedAt' | 'bookingReference'>): Promise<string> {
    const now = new Date()
    const bookingReference = `BVR-${now.getFullYear()}${(now.getMonth() + 1).toString().padStart(2, '0')}${now.getDate().toString().padStart(2, '0')}-${Math.random().toString(36).substring(2, 8).toUpperCase()}`
    
    const docRef = await addDoc(collection(db, 'bookings'), {
      ...data,
      bookingReference,
      createdAt: Timestamp.fromDate(now),
      updatedAt: Timestamp.fromDate(now)
    })
    return docRef.id
  },

  async update(bookingId: string, data: Partial<Booking>): Promise<void> {
    const docRef = doc(db, 'bookings', bookingId)
    await updateDoc(docRef, {
      ...data,
      updatedAt: Timestamp.now()
    })
  }
}

// Review operations (subcollection under farms)
export const reviewService = {
  async getByFarm(farmId: string): Promise<Review[]> {
    const q = query(
      collection(db, 'farms', farmId, 'reviews'),
      where('isPublic', '==', true),
      orderBy('createdAt', 'desc')
    )
    const querySnapshot = await getDocs(q)
    return querySnapshot.docs.map(doc => docToData<Review>(doc)!).filter(Boolean)
  },

  async create(farmId: string, data: Omit<Review, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {
    const now = new Date()
    const docRef = await addDoc(collection(db, 'farms', farmId, 'reviews'), {
      ...data,
      createdAt: Timestamp.fromDate(now),
      updatedAt: Timestamp.fromDate(now)
    })
    return docRef.id
  },

  async update(farmId: string, reviewId: string, data: Partial<Review>): Promise<void> {
    const docRef = doc(db, 'farms', farmId, 'reviews', reviewId)
    await updateDoc(docRef, {
      ...data,
      updatedAt: Timestamp.now()
    })
  }
}

// Species operations
export const speciesService = {
  async getAll(): Promise<GameSpecies[]> {
    const q = query(collection(db, 'species'), orderBy('name'))
    const querySnapshot = await getDocs(q)
    return querySnapshot.docs.map(doc => docToData<GameSpecies>(doc)!).filter(Boolean)
  },

  async get(speciesId: string): Promise<GameSpecies | null> {
    const docRef = doc(db, 'species', speciesId)
    const docSnap = await getDoc(docRef)
    return docToData<GameSpecies>(docSnap)
  }
}

// Amenities operations
export const amenityService = {
  async getAll(): Promise<FarmAmenity[]> {
    const q = query(collection(db, 'amenities'), orderBy('name'))
    const querySnapshot = await getDocs(q)
    return querySnapshot.docs.map(doc => docToData<FarmAmenity>(doc)!).filter(Boolean)
  }
}
