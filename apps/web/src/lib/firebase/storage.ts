import {
  deleteObject,
  listAll,
  getMetadata,
  updateMetadata,
  StorageReference,
  ref,
  getDownloadURL
} from 'firebase/storage'
import { storage } from './client'
import { auth } from './config'

export interface UploadProgress {
  bytesTransferred: number
  totalBytes: number
  progress: number
}

export interface UploadOptions {
  onProgress?: (progress: UploadProgress) => void
  onError?: (error: Error) => void
  onComplete?: (downloadURL: string) => void
}

// Get the Cloud Function URL based on environment
function getCloudFunctionURL(): string {
  const projectId = process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID

  if (!projectId) {
    throw new Error('NEXT_PUBLIC_FIREBASE_PROJECT_ID environment variable is required')
  }

  if (process.env.NODE_ENV === 'development' && process.env.NEXT_PUBLIC_USE_FIREBASE_EMULATORS === 'true') {
    return `http://localhost:5001/${projectId}/us-central1/uploadImage`
  }

  // Production URL
  return `https://us-central1-${projectId}.cloudfunctions.net/uploadImage`
}



// Upload file using Cloud Function
async function uploadFileViaCloudFunction(
  file: File,
  bucket: 'farm-images' | 'profile-images',
  farmId?: string,
  userId?: string,
  options?: UploadOptions
): Promise<string> {
  try {
    // Get authentication token
    const user = auth.currentUser
    if (!user) {
      throw new Error('User must be authenticated to upload files')
    }

    const token = await user.getIdToken()

    // Create FormData
    const formData = new FormData()
    formData.append('file', file)
    formData.append('bucket', bucket)

    if (farmId) {
      formData.append('farmId', farmId)
    }
    if (userId) {
      formData.append('userId', userId)
    }

    // Simulate progress tracking since we can't get real progress from fetch
    const simulateProgress = () => {
      if (!options?.onProgress) return

      let progress = 0
      const interval = setInterval(() => {
        progress += Math.random() * 20
        if (progress >= 90) {
          clearInterval(interval)
          return
        }

        options.onProgress?.({
          bytesTransferred: Math.floor((progress / 100) * file.size),
          totalBytes: file.size,
          progress
        })
      }, 200)

      return interval
    }

    const progressInterval = simulateProgress()

    try {
      // Upload to Cloud Function
      const response = await fetch(getCloudFunctionURL(), {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`
        },
        body: formData
      })

      // Clear progress simulation
      if (progressInterval) {
        clearInterval(progressInterval)
      }

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(errorData.error || `Upload failed with status ${response.status}`)
      }

      const result = await response.json()

      if (!result.success || !result.downloadURL) {
        throw new Error(result.error || 'Upload failed')
      }

      // Complete progress
      options?.onProgress?.({
        bytesTransferred: file.size,
        totalBytes: file.size,
        progress: 100
      })

      options?.onComplete?.(result.downloadURL)

      return result.downloadURL

    } catch (error) {
      // Clear progress simulation on error
      if (progressInterval) {
        clearInterval(progressInterval)
      }

      const uploadError = error instanceof Error ? error : new Error('Upload failed')
      options?.onError?.(uploadError)
      throw uploadError
    }

  } catch (error) {
    console.error('Error uploading file via Cloud Function:', error)
    const uploadError = error instanceof Error ? error : new Error('Upload failed')
    options?.onError?.(uploadError)
    throw uploadError
  }
}

// Storage service for Firebase Storage operations (read-only operations)
export const storageService = {

  // Delete a file from Firebase Storage
  async deleteFile(path: string): Promise<void> {
    try {
      const storageRef = ref(storage, path)
      await deleteObject(storageRef)
    } catch (error) {
      console.error('Error deleting file:', error)
      throw error
    }
  },

  // Get download URL for a file
  async getDownloadURL(path: string): Promise<string> {
    try {
      const storageRef = ref(storage, path)
      return await getDownloadURL(storageRef)
    } catch (error) {
      console.error('Error getting download URL:', error)
      throw error
    }
  },

  // List all files in a directory
  async listFiles(path: string): Promise<StorageReference[]> {
    try {
      const storageRef = ref(storage, path)
      const result = await listAll(storageRef)
      return result.items
    } catch (error) {
      console.error('Error listing files:', error)
      throw error
    }
  },

  // Get file metadata
  async getMetadata(path: string) {
    try {
      const storageRef = ref(storage, path)
      return await getMetadata(storageRef)
    } catch (error) {
      console.error('Error getting metadata:', error)
      throw error
    }
  },

  // Update file metadata
  async updateMetadata(path: string, metadata: Record<string, string>) {
    try {
      const storageRef = ref(storage, path)
      return await updateMetadata(storageRef, metadata)
    } catch (error) {
      console.error('Error updating metadata:', error)
      throw error
    }
  }
}

// Helper functions for specific use cases
export const farmImageService = {
  // Upload farm image using Cloud Function
  async uploadFarmImage(
    farmId: string,
    file: File,
    options?: UploadOptions
  ): Promise<string> {
    console.log('Using Cloud Function for farm image upload')
    return await uploadFileViaCloudFunction(
      file,
      'farm-images',
      farmId,
      undefined,
      options
    )
  },

  // Delete farm image
  async deleteFarmImage(farmId: string, imageUrl: string): Promise<void> {
    try {
      // Extract path from Firebase Storage URL
      const path = extractPathFromURL(imageUrl)
      if (path && path.startsWith(`farm-images/${farmId}/`)) {
        await storageService.deleteFile(path)
      }
    } catch (error) {
      console.error('Error deleting farm image:', error)
      throw error
    }
  },

  // List all images for a farm
  async listFarmImages(farmId: string): Promise<string[]> {
    try {
      const files = await storageService.listFiles(`farm-images/${farmId}`)
      const urls = await Promise.all(
        files.map(file => getDownloadURL(file))
      )
      return urls
    } catch (error) {
      console.error('Error listing farm images:', error)
      throw error
    }
  }
}

export const profileImageService = {
  // Upload profile image using Cloud Function
  async uploadProfileImage(
    userId: string,
    file: File,
    options?: UploadOptions
  ): Promise<string> {
    console.log('Using Cloud Function for profile image upload')
    return await uploadFileViaCloudFunction(
      file,
      'profile-images',
      undefined,
      userId,
      options
    )
  },

  // Delete profile image
  async deleteProfileImage(userId: string, imageUrl: string): Promise<void> {
    try {
      const path = extractPathFromURL(imageUrl)
      if (path && path.startsWith(`profile-images/${userId}/`)) {
        await storageService.deleteFile(path)
      }
    } catch (error) {
      console.error('Error deleting profile image:', error)
      throw error
    }
  }
}

// Helper function to extract storage path from Firebase Storage URL
function extractPathFromURL(url: string): string | null {
  try {
    const urlObj = new URL(url)
    if (urlObj.hostname === 'firebasestorage.googleapis.com') {
      const pathMatch = urlObj.pathname.match(/\/o\/(.+)\?/)
      if (pathMatch) {
        return decodeURIComponent(pathMatch[1])
      }
    }
    return null
  } catch (error) {
    console.error('Error extracting path from URL:', error)
    return null
  }
}
