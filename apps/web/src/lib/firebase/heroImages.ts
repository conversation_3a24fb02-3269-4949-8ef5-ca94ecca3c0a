import { ref, listAll, getDownloadURL } from 'firebase/storage'
import { storage } from './config'

/**
 * Hero image service for fetching random background images from Firebase Storage
 */

// Default banner image path (relative to public folder)
export const DEFAULT_HERO_IMAGE = '/default-banner.JPG'

export interface HeroImageMetadata {
  name: string
  fullPath: string
  downloadURL: string
}

/**
 * Fetches a random hero image URL from Firebase Storage
 * @returns Promise<string> - Download URL of a random hero image, or default banner if none available
 */
export async function getRandomHeroImageUrl(): Promise<string> {
  try {
    // Reference to the hero-images folder in Firebase Storage
    const heroImagesRef = ref(storage, 'hero-images/')

    // List all items in the hero-images folder
    const listResult = await listAll(heroImagesRef)

    // Check if there are any images available
    if (!listResult.items || listResult.items.length === 0) {
      console.warn('No hero images found in Firebase Storage hero-images folder, using default banner')
      return DEFAULT_HERO_IMAGE
    }

    // Pick a random image from the available items
    const randomIndex = Math.floor(Math.random() * listResult.items.length)
    const randomImageRef = listResult.items[randomIndex]

    // Get the download URL for the selected image
    const downloadURL = await getDownloadURL(randomImageRef)

    return downloadURL
  } catch (error) {
    // Fallback to default banner on error
    console.error('Error fetching random hero image from Firebase Storage, using default banner:', error)
    return DEFAULT_HERO_IMAGE
  }
}

/**
 * Gets the default hero image URL (immediate, no async needed)
 * @returns string - Path to the default banner image
 */
export function getDefaultHeroImageUrl(): string {
  return DEFAULT_HERO_IMAGE
}

/**
 * Fetches all available hero images with metadata
 * @returns Promise<HeroImageMetadata[]> - Array of hero image metadata
 */
export async function getAllHeroImages(): Promise<HeroImageMetadata[]> {
  try {
    const heroImagesRef = ref(storage, 'hero-images/')
    const listResult = await listAll(heroImagesRef)
    
    if (!listResult.items || listResult.items.length === 0) {
      return []
    }
    
    // Get download URLs for all images
    const heroImages: HeroImageMetadata[] = await Promise.all(
      listResult.items.map(async (item) => {
        const downloadURL = await getDownloadURL(item)
        return {
          name: item.name,
          fullPath: item.fullPath,
          downloadURL
        }
      })
    )
    
    return heroImages
  } catch (error) {
    console.error('Error fetching all hero images from Firebase Storage:', error)
    return []
  }
}

/**
 * Preloads a random hero image for better performance
 * @returns Promise<string | null> - Download URL of the preloaded image
 */
export async function preloadRandomHeroImage(): Promise<string | null> {
  const imageUrl = await getRandomHeroImageUrl()
  
  if (imageUrl) {
    // Create an image element to preload the image
    const img = new Image()
    img.src = imageUrl
    
    // Return the URL after initiating preload
    return imageUrl
  }
  
  return null
}
