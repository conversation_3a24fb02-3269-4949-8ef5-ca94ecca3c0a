import { NextResponse, type NextRequest } from 'next/server'

export async function updateSession(request: NextRequest) {
  const response = NextResponse.next({
    request,
  })

  // Define public routes that don't require authentication
  const publicRoutes = [
    '/',
    '/farms',
    '/how-it-works',
    '/about',
    '/contact',
    '/privacy',
    '/terms',
    '/help',
    '/safety',
    '/faq',
    '/farm-owners'
  ]

  // Check if the current path is public or auth-related
  const isPublicRoute = publicRoutes.some(route =>
    request.nextUrl.pathname === route ||
    request.nextUrl.pathname.startsWith('/auth/') ||
    request.nextUrl.pathname.startsWith('/api/') ||
    request.nextUrl.pathname.startsWith('/_next/') ||
    request.nextUrl.pathname.includes('.')
  )

  // If it's a public route, allow access
  if (isPublicRoute) {
    return response
  }

  // For protected routes, check if user has a session token
  const idToken = request.cookies.get('firebase-token')?.value

  if (!idToken || idToken.trim() === '') {
    // No valid token found, redirect to login
    // But avoid redirect loops by checking if we're already going to login
    if (request.nextUrl.pathname !== '/auth/login') {
      return NextResponse.redirect(new URL('/auth/login', request.url))
    }
  }

  // Token exists, allow access (verification happens server-side)
  return response
}
