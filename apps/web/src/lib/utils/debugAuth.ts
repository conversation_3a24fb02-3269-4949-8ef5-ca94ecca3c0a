import { auth } from '@/lib/firebase/client'

/**
 * Debug utility to check authentication state and custom claims
 */
export async function debugAuthState() {
  const user = auth.currentUser
  
  if (!user) {
    console.log('🔍 Auth Debug: No user logged in')
    return null
  }

  try {
    // Get the ID token result to see custom claims
    const idTokenResult = await user.getIdTokenResult(true) // Force refresh
    
    console.log('🔍 Auth Debug Information:')
    console.log('  User UID:', user.uid)
    console.log('  Email:', user.email)
    console.log('  Display Name:', user.displayName)
    console.log('  Email Verified:', user.emailVerified)
    console.log('  Custom Claims:', idTokenResult.claims)
    console.log('  Role from Claims:', idTokenResult.claims.role || 'NO ROLE SET')
    console.log('  Token Expiration:', new Date(idTokenResult.expirationTime))
    console.log('  Auth Time:', new Date(idTokenResult.authTime))
    console.log('  Issued At:', new Date(idTokenResult.issuedAtTime))
    
    // Check if role is set
    if (!idTokenResult.claims.role) {
      console.warn('⚠️  WARNING: No role set in custom claims!')
      console.log('   This will cause Firestore permission errors.')
      console.log('   The user may need to re-register or have their role set manually.')
    }
    
    return {
      uid: user.uid,
      email: user.email,
      role: (idTokenResult.claims.role as string) || 'NO ROLE',
      claims: idTokenResult.claims as Record<string, unknown>,
      hasRole: !!idTokenResult.claims.role
    }
    
  } catch (error) {
    console.error('🔍 Auth Debug Error:', error)
    return null
  }
}

/**
 * Check if the current user has the required role
 */
export async function checkUserRole(requiredRole?: string) {
  const debugInfo = await debugAuthState()
  
  if (!debugInfo) {
    return false
  }
  
  if (requiredRole) {
    const hasRole = debugInfo.role === requiredRole
    console.log(`🔍 Role Check: Required "${requiredRole}", User has "${debugInfo.role}" - ${hasRole ? 'PASS' : 'FAIL'}`)
    return hasRole
  }
  
  return !!debugInfo.hasRole
}

/**
 * Force refresh the user's ID token to get latest custom claims
 */
export async function refreshUserToken() {
  const user = auth.currentUser
  
  if (!user) {
    console.log('🔄 Token Refresh: No user logged in')
    return false
  }
  
  try {
    console.log('🔄 Refreshing user token...')
    await user.getIdToken(true) // Force refresh
    console.log('✅ Token refreshed successfully')
    return true
  } catch (error) {
    console.error('❌ Token refresh failed:', error)
    return false
  }
}
