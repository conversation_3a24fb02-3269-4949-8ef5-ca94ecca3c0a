import { UserRole } from '@/lib/types/firestore'

/**
 * Utility functions for role management and validation
 */

/**
 * Validates if a role is valid
 */
export function isValidRole(role: string): role is UserRole {
  return role === 'farm_owner' || role === 'guest' || role === 'admin'
}

/**
 * Checks if a user has permission to perform admin actions
 */
export function isAdmin(userRole: UserRole | null | undefined): boolean {
  return userRole === 'admin'
}

/**
 * Checks if a user is a farm owner
 */
export function isFarmOwner(userRole: UserRole | null | undefined): boolean {
  return userRole === 'farm_owner' || userRole === 'admin'
}

/**
 * Checks if a user is a guest (hunter, photo safari guest, visitor)
 */
export function isGuest(userRole: UserRole | null | undefined): boolean {
  return userRole === 'guest' || userRole === 'admin'
}

/**
 * Gets user-friendly role display name
 */
export function getRoleDisplayName(role: UserRole): string {
  switch (role) {
    case 'farm_owner':
      return 'Farm Owner'
    case 'guest':
      return 'Guest'
    case 'admin':
      return 'Administrator'
    default:
      return 'Unknown'
  }
}

/**
 * Gets role description
 */
export function getRoleDescription(role: UserRole): string {
  switch (role) {
    case 'farm_owner':
      return 'Can manage farms, upload farm images, and handle bookings'
    case 'guest':
      return 'Can browse farms, make bookings, and upload profile images'
    case 'admin':
      return 'Full system access with administrative privileges'
    default:
      return 'Unknown role'
  }
}

/**
 * Validates role assignment permissions
 * Returns true if the current user can assign the target role
 */
export function canAssignRole(
  currentUserRole: UserRole | null | undefined,
  targetRole: UserRole
): boolean {
  // Only admins can assign roles
  if (!isAdmin(currentUserRole)) {
    return false
  }

  // Validate that the target role is valid
  if (!isValidRole(targetRole)) {
    return false
  }

  // Admins can assign any role
  return true
}

/**
 * Gets available roles for assignment based on current user's permissions
 */
export function getAvailableRoles(currentUserRole: UserRole | null | undefined): UserRole[] {
  if (isAdmin(currentUserRole)) {
    return ['farm_owner', 'guest', 'admin']
  }
  
  // Non-admins cannot assign roles
  return []
}

/**
 * Validates if a role change is allowed
 */
export function isRoleChangeAllowed(
  currentUserRole: UserRole | null | undefined,
  targetUserId: string,
  currentUserId: string,
  newRole: UserRole
): { allowed: boolean; reason?: string } {
  // Users can only change their own role during registration
  if (targetUserId === currentUserId && !currentUserRole) {
    // This is likely during registration
    if (newRole === 'farm_owner' || newRole === 'guest') {
      return { allowed: true }
    }
    return { allowed: false, reason: 'Cannot assign admin role during registration' }
  }

  // Only admins can change other users' roles
  if (!isAdmin(currentUserRole)) {
    return { allowed: false, reason: 'Only administrators can change user roles' }
  }

  // Admins can change any role
  return { allowed: true }
}
