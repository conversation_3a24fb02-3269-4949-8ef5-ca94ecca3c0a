'use client'

import { useAuth } from '@/hooks/useAuth'
import { FarmOwnerDashboard } from './FarmOwnerDashboard'
import { GuestDashboard } from './GuestDashboard'
import { LoadingSpinner } from '@/components/ui/LoadingSpinner'
import { debugAuthState } from '@/lib/utils/debugAuth'
import { AuthDebugPanel } from '@/components/debug/AuthDebugPanel'
import { useEffect } from 'react'

export function UnifiedDashboard() {
  const { userProfile, loading } = useAuth()

  // Debug authentication state when component mounts
  useEffect(() => {
    if (!loading && userProfile) {
      debugAuthState()
    }
  }, [loading, userProfile])

  if (loading || !userProfile) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  // Render dashboard based on user role
  return (
    <div>
      {/* Temporary debug panel - remove in production */}
      <AuthDebugPanel />

      {userProfile.role === 'farm_owner' ? (
        <FarmOwnerDashboard />
      ) : (
        <GuestDashboard />
      )}
    </div>
  )
}
