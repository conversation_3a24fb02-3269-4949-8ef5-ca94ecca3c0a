'use client'

import { useState, useEffect } from 'react'
import { useAuth } from '@/hooks/useAuth'
import { Button } from '@/components/ui/Button'
import { Card } from '@/components/ui/Card'
import { LoadingSpinner } from '@/components/ui/LoadingSpinner'
import { bookingService, farmService } from '@/lib/firebase/firestore'
import { Booking, GameFarm } from '@/lib/types/firestore'
import { Calendar, MapPin, Heart, Search, Clock, CheckCircle } from 'lucide-react'
import Link from 'next/link'

export function GuestDashboard() {
  const { userProfile } = useAuth()
  const [bookings, setBookings] = useState<Booking[]>([])
  const [savedFarms, setSavedFarms] = useState<GameFarm[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchData = async () => {
      if (!userProfile?.id) return

      try {
        setLoading(true)
        setError(null)

        // Fetch user's bookings using filters to respect security rules
        const userBookings = await bookingService.getAll({
          hunterId: userProfile.id,
          limit: 10
        })
        setBookings(userBookings)

        // Fetch active farms that guests can view (public farms)
        const activeFarms = await farmService.getActive(3) // Get first 3 active farms
        setSavedFarms(activeFarms)

      } catch (err) {
        console.error('Error fetching dashboard data:', err)
        setError('Failed to load dashboard data')
      } finally {
        setLoading(false)
      }
    }

    fetchData()
  }, [userProfile?.id])

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <p className="text-red-600 mb-4">{error}</p>
          <Button onClick={() => window.location.reload()}>
            Try Again
          </Button>
        </div>
      </div>
    )
  }

  // Calculate statistics
  const totalBookings = bookings.length
  const upcomingBookings = bookings.filter(b => 
    new Date(b.startDate) > new Date() && b.status === 'confirmed'
  ).length
  const completedBookings = bookings.filter(b => 
    new Date(b.endDate) < new Date() && b.status === 'confirmed'
  ).length

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-earth-900">
              Welcome back, {userProfile?.firstName || 'Guest'}!
            </h1>
            <p className="text-earth-600 mt-2">
              Ready for your next safari adventure?
            </p>
          </div>
          <Link href="/farms">
            <Button variant="primary" className="flex items-center gap-2">
              <Search className="w-4 h-4" />
              Browse Farms
            </Button>
          </Link>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <Card className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-earth-600 text-sm font-medium">Total Bookings</p>
              <p className="text-2xl font-bold text-earth-900">{totalBookings}</p>
            </div>
            <Calendar className="w-8 h-8 text-accent-600" />
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-earth-600 text-sm font-medium">Upcoming Trips</p>
              <p className="text-2xl font-bold text-earth-900">{upcomingBookings}</p>
            </div>
            <Clock className="w-8 h-8 text-accent-600" />
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-earth-600 text-sm font-medium">Completed Trips</p>
              <p className="text-2xl font-bold text-earth-900">{completedBookings}</p>
            </div>
            <CheckCircle className="w-8 h-8 text-accent-600" />
          </div>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* My Bookings */}
        <Card className="p-6">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-semibold text-earth-900">My Bookings</h2>
            <Link href="/dashboard/bookings">
              <Button variant="outline" size="sm">
                View All
              </Button>
            </Link>
          </div>

          {bookings.length === 0 ? (
            <div className="text-center py-8">
              <Calendar className="w-12 h-12 text-earth-400 mx-auto mb-4" />
              <p className="text-earth-600 mb-4">You haven&apos;t made any bookings yet</p>
              <Link href="/farms">
                <Button variant="primary">Browse Farms</Button>
              </Link>
            </div>
          ) : (
            <div className="space-y-4">
              {bookings.slice(0, 3).map((booking) => (
                <div key={booking.id} className="flex items-center justify-between p-4 bg-earth-50 rounded-lg">
                  <div>
                    <h3 className="font-medium text-earth-900">Booking #{booking.id.slice(-6)}</h3>
                    <p className="text-sm text-earth-600">
                      {new Date(booking.startDate).toLocaleDateString()} - {new Date(booking.endDate).toLocaleDateString()}
                    </p>
                  </div>
                  <div className="text-right">
                    <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${
                      booking.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                      booking.status === 'confirmed' ? 'bg-green-100 text-green-800' :
                      'bg-red-100 text-red-800'
                    }`}>
                      {booking.status}
                    </span>
                    <p className="text-sm font-medium text-earth-900 mt-1">
                      R{booking.totalPrice?.toLocaleString()}
                    </p>
                  </div>
                </div>
              ))}
              {bookings.length > 3 && (
                <div className="text-center pt-4">
                  <Link href="/dashboard/bookings">
                    <Button variant="outline">View All Bookings</Button>
                  </Link>
                </div>
              )}
            </div>
          )}
        </Card>

        {/* Recommended Farms */}
        <Card className="p-6">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-semibold text-earth-900">Recommended Farms</h2>
            <Link href="/farms">
              <Button variant="outline" size="sm">
                Browse All
              </Button>
            </Link>
          </div>

          {savedFarms.length === 0 ? (
            <div className="text-center py-8">
              <Heart className="w-12 h-12 text-earth-400 mx-auto mb-4" />
              <p className="text-earth-600 mb-4">Discover amazing safari destinations</p>
              <Link href="/farms">
                <Button variant="primary">Explore Farms</Button>
              </Link>
            </div>
          ) : (
            <div className="space-y-4">
              {savedFarms.map((farm) => (
                <div key={farm.id} className="flex items-center justify-between p-4 bg-earth-50 rounded-lg">
                  <div>
                    <h3 className="font-medium text-earth-900">{farm.name}</h3>
                    <p className="text-sm text-earth-600 flex items-center gap-1">
                      <MapPin className="w-3 h-3" />
                      {farm.location}
                    </p>
                  </div>
                  <div className="text-right">
                    <p className="text-sm font-medium text-earth-900">
                      R{farm.pricePerDay?.toLocaleString()}/day
                    </p>
                    <Link href={`/farms/${farm.id}`}>
                      <Button variant="outline" size="sm">
                        View Details
                      </Button>
                    </Link>
                  </div>
                </div>
              ))}
            </div>
          )}
        </Card>
      </div>

      {/* Quick Actions */}
      <Card className="p-6 mt-8">
        <h2 className="text-xl font-semibold text-earth-900 mb-6">Quick Actions</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Link href="/farms">
            <Button variant="outline" className="w-full flex items-center gap-2">
              <Search className="w-4 h-4" />
              Browse Farms
            </Button>
          </Link>
          <Link href="/profile">
            <Button variant="outline" className="w-full flex items-center gap-2">
              <Calendar className="w-4 h-4" />
              Manage Profile
            </Button>
          </Link>
          <Link href="/dashboard/bookings">
            <Button variant="outline" className="w-full flex items-center gap-2">
              <Clock className="w-4 h-4" />
              View Bookings
            </Button>
          </Link>
        </div>
      </Card>
    </div>
  )
}
