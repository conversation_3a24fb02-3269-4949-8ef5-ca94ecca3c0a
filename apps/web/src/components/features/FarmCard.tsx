import Link from 'next/link'
import { Card, CardContent, CardImage, CardTitle } from '@/components/ui/Card'
import { Badge } from '@/components/ui/Badge'

export interface FarmCardProps {
  id: string
  name: string
  location: string
  province: string
  description: string
  imageUrl?: string
  activities: ('hunting' | 'photo_safari')[]
  priceRange?: string
  rating?: number
  reviewCount?: number
}

export function FarmCard({
  id,
  name,
  location,
  province,
  description,
  imageUrl,
  activities,
  priceRange,
  rating,
  reviewCount
}: FarmCardProps) {
  return (
    <Link href={`/farms/${id}`}>
      <Card className="h-full cursor-pointer">
        <CardImage src={imageUrl} alt={`${name} farm`} />
        
        <CardContent className="flex flex-col h-full">
          <div className="flex-1">
            <div className="flex items-start justify-between mb-3">
              <CardTitle className="text-lg leading-tight">{name}</CardTitle>
              {rating && (
                <div className="flex items-center text-sm text-earth-600">
                  <span className="text-accent-600 mr-1">★</span>
                  <span>{rating}</span>
                  {reviewCount && <span className="ml-1">({reviewCount})</span>}
                </div>
              )}
            </div>

            <div className="text-earth-600 text-sm mb-3">
              {location}, {province}
            </div>

            <p className="text-earth-700 text-sm mb-4 line-clamp-3">
              {description}
            </p>

            <div className="flex flex-wrap gap-2 mb-4">
              {activities.map((activity) => (
                <Badge 
                  key={activity} 
                  variant={activity === 'hunting' ? 'hunting' : 'photo'}
                >
                  {activity === 'hunting' ? 'Hunting' : 'Photo Safari'}
                </Badge>
              ))}
            </div>
          </div>

          {priceRange && (
            <div className="text-earth-900 font-semibold text-lg">
              {priceRange}
            </div>
          )}
        </CardContent>
      </Card>
    </Link>
  )
}