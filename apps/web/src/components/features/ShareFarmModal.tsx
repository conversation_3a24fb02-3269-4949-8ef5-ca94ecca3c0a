'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import { X, Copy, Share2, Mail, MessageCircle } from 'lucide-react'

interface ShareFarmModalProps {
  isOpen: boolean
  onClose: () => void
  farmId: string
  farmName: string
}

export function ShareFarmModal({ isOpen, onClose, farmId, farmName }: ShareFarmModalProps) {
  const [copied, setCopied] = useState(false)

  if (!isOpen) return null

  const farmUrl = `${window.location.origin}/farms/${farmId}`
  const shareText = `Check out ${farmName} on BVR Safaris - an amazing hunting and photo safari destination!`

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(farmUrl)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    } catch (err) {
      console.error('Failed to copy:', err)
    }
  }

  const shareViaEmail = () => {
    const subject = encodeURIComponent(`Check out ${farmName} on BVR Safaris`)
    const body = encodeURIComponent(`${shareText}\n\n${farmUrl}`)
    window.open(`mailto:?subject=${subject}&body=${body}`)
  }

  const shareViaWhatsApp = () => {
    const text = encodeURIComponent(`${shareText} ${farmUrl}`)
    window.open(`https://wa.me/?text=${text}`)
  }

  const shareViaFacebook = () => {
    const url = encodeURIComponent(farmUrl)
    window.open(`https://www.facebook.com/sharer/sharer.php?u=${url}`)
  }

  const shareViaTwitter = () => {
    const text = encodeURIComponent(shareText)
    const url = encodeURIComponent(farmUrl)
    window.open(`https://twitter.com/intent/tweet?text=${text}&url=${url}`)
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <Card className="w-full max-w-md">
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Share2 className="w-5 h-5" />
              Share Farm Listing
            </CardTitle>
            <Button
              variant="outline"
              size="sm"
              onClick={onClose}
              className="p-2"
            >
              <X className="w-4 h-4" />
            </Button>
          </div>
        </CardHeader>

        <CardContent className="space-y-6">
          {/* Farm URL */}
          <div>
            <label className="block text-sm font-medium text-earth-700 mb-2">
              Farm URL
            </label>
            <div className="flex gap-2">
              <Input
                value={farmUrl}
                readOnly
                className="flex-1"
              />
              <Button
                variant="outline"
                onClick={copyToClipboard}
                className="px-3"
              >
                <Copy className="w-4 h-4" />
              </Button>
            </div>
            {copied && (
              <p className="text-green-600 text-sm mt-1">URL copied to clipboard!</p>
            )}
          </div>

          {/* Social Sharing */}
          <div>
            <label className="block text-sm font-medium text-earth-700 mb-3">
              Share on Social Media
            </label>
            <div className="grid grid-cols-2 gap-3">
              <Button
                variant="outline"
                onClick={shareViaFacebook}
                className="flex items-center gap-2 justify-center"
              >
                <Share2 className="w-4 h-4 text-blue-600" />
                Facebook
              </Button>

              <Button
                variant="outline"
                onClick={shareViaTwitter}
                className="flex items-center gap-2 justify-center"
              >
                <Share2 className="w-4 h-4 text-blue-400" />
                Twitter
              </Button>

              <Button
                variant="outline"
                onClick={shareViaWhatsApp}
                className="flex items-center gap-2 justify-center"
              >
                <MessageCircle className="w-4 h-4 text-green-600" />
                WhatsApp
              </Button>

              <Button
                variant="outline"
                onClick={shareViaEmail}
                className="flex items-center gap-2 justify-center"
              >
                <Mail className="w-4 h-4 text-gray-600" />
                Email
              </Button>
            </div>
          </div>

          {/* Share Tips */}
          <div className="bg-earth-50 p-4 rounded-lg">
            <h4 className="font-medium text-earth-900 mb-2">Sharing Tips</h4>
            <ul className="text-sm text-earth-600 space-y-1">
              <li>• Share on social media to reach more potential customers</li>
              <li>• Include high-quality photos in your posts</li>
              <li>• Use relevant hashtags like #hunting #safari #southafrica</li>
              <li>• Engage with comments and questions promptly</li>
            </ul>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-3">
            <Button
              variant="outline"
              onClick={onClose}
              className="flex-1"
            >
              Close
            </Button>
            <Button
              variant="primary"
              onClick={copyToClipboard}
              className="flex-1"
            >
              <Copy className="w-4 h-4 mr-2" />
              Copy Link
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
