'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import { Star } from 'lucide-react'
import { reviewService } from '@/lib/firebase/firestore'
import { useAuth } from '@/hooks/useAuth'

interface ReviewFormProps {
  bookingId: string
  farmId: string
  farmName: string
  onSubmit?: () => void
  onCancel?: () => void
}

export function ReviewForm({ bookingId, farmId, farmName, onSubmit, onCancel }: ReviewFormProps) {
  const { user } = useAuth()

  const [formData, setFormData] = useState({
    rating: 0,
    title: '',
    comment: '',
    wouldRecommend: true
  })
  const [hoveredRating, setHoveredRating] = useState(0)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const handleRatingClick = (rating: number) => {
    setFormData(prev => ({ ...prev, rating }))
  }

  const handleInputChange = (field: string, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    setError(null)
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!user) {
      setError('You must be logged in to submit a review')
      return
    }

    if (formData.rating === 0) {
      setError('Please select a rating')
      return
    }

    if (!formData.comment.trim()) {
      setError('Please write a comment')
      return
    }

    setIsSubmitting(true)
    setError(null)

    try {
      await reviewService.create(farmId, {
        farmId,
        bookingId,
        reviewerId: user.uid,
        rating: formData.rating,
        title: formData.title.trim() || undefined,
        comment: formData.comment.trim(),
        wouldRecommend: formData.wouldRecommend,
        isPublic: true
      })

      // Reset form
      setFormData({
        rating: 0,
        title: '',
        comment: '',
        wouldRecommend: true
      })

      onSubmit?.()
    } catch (err) {
      console.error('Error submitting review:', err)
      setError(err instanceof Error ? err.message : 'Failed to submit review')
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Card className="max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle>Write a Review for {farmName}</CardTitle>
        <p className="text-earth-600">
          Share your experience to help other travelers make informed decisions.
        </p>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-md p-3">
              <p className="text-red-600 text-sm">{error}</p>
            </div>
          )}

          {/* Rating */}
          <div>
            <label className="block text-sm font-medium text-earth-900 mb-3">
              Overall Rating *
            </label>
            <div className="flex items-center space-x-1">
              {[1, 2, 3, 4, 5].map((star) => (
                <button
                  key={star}
                  type="button"
                  onClick={() => handleRatingClick(star)}
                  onMouseEnter={() => setHoveredRating(star)}
                  onMouseLeave={() => setHoveredRating(0)}
                  className="p-1 transition-colors"
                >
                  <Star
                    className={`w-8 h-8 ${
                      star <= (hoveredRating || formData.rating)
                        ? 'text-yellow-400 fill-current'
                        : 'text-gray-300'
                    }`}
                  />
                </button>
              ))}
              <span className="ml-3 text-earth-700">
                {formData.rating > 0 && (
                  <>
                    {formData.rating} star{formData.rating !== 1 ? 's' : ''}
                    {formData.rating === 5 && ' - Excellent!'}
                    {formData.rating === 4 && ' - Very Good'}
                    {formData.rating === 3 && ' - Good'}
                    {formData.rating === 2 && ' - Fair'}
                    {formData.rating === 1 && ' - Poor'}
                  </>
                )}
              </span>
            </div>
          </div>

          {/* Title */}
          <Input
            label="Review Title (Optional)"
            value={formData.title}
            onChange={(e) => handleInputChange('title', e.target.value)}
            placeholder="Summarize your experience..."
            maxLength={100}
          />

          {/* Comment */}
          <div>
            <label className="block text-sm font-medium text-earth-900 mb-2">
              Your Review *
            </label>
            <textarea
              className="w-full px-3 py-2 border border-earth-300 rounded-md focus:outline-none focus:ring-2 focus:ring-accent-600 focus:border-transparent"
              rows={6}
              value={formData.comment}
              onChange={(e) => handleInputChange('comment', e.target.value)}
              placeholder="Tell us about your safari experience. What did you enjoy most? Any tips for future guests?"
              maxLength={1000}
              required
            />
            <p className="text-sm text-earth-500 mt-1">
              {formData.comment.length}/1000 characters
            </p>
          </div>

          {/* Would Recommend */}
          <div>
            <label className="block text-sm font-medium text-earth-900 mb-3">
              Would you recommend this farm to others?
            </label>
            <div className="flex space-x-4">
              <label className="flex items-center">
                <input
                  type="radio"
                  name="recommend"
                  checked={formData.wouldRecommend === true}
                  onChange={() => handleInputChange('wouldRecommend', true)}
                  className="mr-2 text-accent-600 focus:ring-accent-600"
                />
                <span className="text-earth-700">Yes, I would recommend</span>
              </label>
              <label className="flex items-center">
                <input
                  type="radio"
                  name="recommend"
                  checked={formData.wouldRecommend === false}
                  onChange={() => handleInputChange('wouldRecommend', false)}
                  className="mr-2 text-accent-600 focus:ring-accent-600"
                />
                <span className="text-earth-700">No, I would not recommend</span>
              </label>
            </div>
          </div>

          {/* Privacy Notice */}
          <div className="bg-earth-50 border border-earth-200 rounded-md p-4">
            <p className="text-sm text-earth-700">
              <strong>Privacy Notice:</strong> Your review will be publicly visible to help other travelers. 
              Your name will be displayed with your review. You can edit or delete your review later from your dashboard.
            </p>
          </div>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-3 pt-4">
            <Button
              type="submit"
              variant="primary"
              size="lg"
              className="flex-1"
              isLoading={isSubmitting}
              disabled={formData.rating === 0 || !formData.comment.trim()}
            >
              {isSubmitting ? 'Submitting Review...' : 'Submit Review'}
            </Button>
            {onCancel && (
              <Button
                type="button"
                variant="outline"
                size="lg"
                onClick={onCancel}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
            )}
          </div>
        </form>
      </CardContent>
    </Card>
  )
}
