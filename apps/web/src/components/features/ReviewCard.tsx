'use client'

import { Review } from '@/lib/types/firestore'
import { Card, CardContent } from '@/components/ui/Card'
import { Badge } from '@/components/ui/Badge'
import { formatDate } from '@/lib/utils'

interface ReviewCardProps {
  review: Review
  showFarmName?: boolean
}

export function ReviewCard({ review }: ReviewCardProps) {

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <svg
        key={i}
        className={`w-4 h-4 ${
          i < rating ? 'text-yellow-400 fill-current' : 'text-earth-300'
        }`}
        viewBox="0 0 20 20"
      >
        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
      </svg>
    ))
  }

  return (
    <Card>
      <CardContent className="p-6">
        <div className="flex items-start justify-between mb-4">
          <div className="flex-1">
            <div className="flex items-center gap-3 mb-2">
              <div className="flex items-center gap-1">
                {renderStars(review.rating)}
              </div>
              <span className="text-sm text-earth-600">
                {formatDate(review.createdAt.toDate().toISOString())}
              </span>
            </div>

            <div className="flex items-center gap-2 mb-2">
              <h4 className="font-semibold text-earth-900">
                Anonymous Reviewer
              </h4>
              {review.wouldRecommend && (
                <Badge variant="hunting">
                  Recommends
                </Badge>
              )}
            </div>
          </div>
        </div>

        {review.title && (
          <h5 className="font-medium text-earth-900 mb-2">
            {review.title}
          </h5>
        )}

        {review.comment && (
          <p className="text-earth-700 mb-4 leading-relaxed">
            {review.comment}
          </p>
        )}

        {review.responseFromOwner && (
          <div className="bg-earth-50 rounded-lg p-4 mt-4">
            <div className="flex items-center gap-2 mb-2">
              <svg className="w-4 h-4 text-accent-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-3.582 8-8 8a8.955 8.955 0 01-2.126-.275c-1.15-.29-2.074-1.214-2.364-2.364A8.955 8.955 0 018 15c0-4.418 3.582-8 8-8s8 3.582 8 8z" />
              </svg>
              <span className="text-sm font-medium text-earth-900">
                Response from farm owner
              </span>
              {review.respondedAt && (
                <span className="text-xs text-earth-500">
                  {formatDate(review.respondedAt.toDate().toISOString())}
                </span>
              )}
            </div>
            <p className="text-earth-700 text-sm">
              {review.responseFromOwner}
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  )
}

interface ReviewSummaryProps {
  reviews: Review[]
  className?: string
}

export function ReviewSummary({ reviews, className = '' }: ReviewSummaryProps) {
  if (reviews.length === 0) {
    return (
      <div className={`text-center py-8 ${className}`}>
        <p className="text-earth-600">No reviews yet</p>
        <p className="text-earth-500 text-sm mt-1">
          Be the first to leave a review after your visit!
        </p>
      </div>
    )
  }

  const averageRating = reviews.reduce((sum, review) => sum + review.rating, 0) / reviews.length
  const roundedRating = Math.round(averageRating * 10) / 10

  const ratingDistribution = Array.from({ length: 5 }, (_, i) => {
    const starCount = 5 - i
    const count = reviews.filter(review => review.rating === starCount).length
    const percentage = reviews.length > 0 ? (count / reviews.length) * 100 : 0
    return { stars: starCount, count, percentage }
  })

  const renderStars = (rating: number, size: 'sm' | 'lg' = 'sm') => {
    const sizeClass = size === 'lg' ? 'w-5 h-5' : 'w-4 h-4'
    return Array.from({ length: 5 }, (_, i) => (
      <svg
        key={i}
        className={`${sizeClass} ${
          i < Math.floor(rating) ? 'text-yellow-400 fill-current' : 'text-earth-300'
        }`}
        viewBox="0 0 20 20"
      >
        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
      </svg>
    ))
  }

  return (
    <div className={className}>
      <div className="bg-white rounded-lg p-6 border">
        <div className="flex items-center gap-6 mb-6">
          <div className="text-center">
            <div className="text-4xl font-bold text-earth-900 mb-1">
              {roundedRating}
            </div>
            <div className="flex items-center gap-1 mb-1">
              {renderStars(averageRating, 'lg')}
            </div>
            <p className="text-sm text-earth-600">
              {reviews.length} review{reviews.length !== 1 ? 's' : ''}
            </p>
          </div>

          <div className="flex-1">
            {ratingDistribution.map(({ stars, count, percentage }) => (
              <div key={stars} className="flex items-center gap-2 mb-1">
                <span className="text-sm text-earth-600 w-6">
                  {stars}
                </span>
                <svg className="w-4 h-4 text-yellow-400 fill-current">
                  <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                </svg>
                <div className="flex-1 bg-earth-200 rounded-full h-2">
                  <div
                    className="bg-yellow-400 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${percentage}%` }}
                  />
                </div>
                <span className="text-sm text-earth-600 w-8">
                  {count}
                </span>
              </div>
            ))}
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
          <div>
            <div className="text-2xl font-bold text-hunting-accent">
              {reviews.filter(r => r.wouldRecommend).length}
            </div>
            <p className="text-sm text-earth-600">Would Recommend</p>
          </div>
          <div>
            <div className="text-2xl font-bold text-photo-accent">
              {reviews.filter(r => r.rating >= 4).length}
            </div>
            <p className="text-sm text-earth-600">4+ Star Reviews</p>
          </div>
          <div>
            <div className="text-2xl font-bold text-accent-600">
              {reviews.filter(r => r.responseFromOwner).length}
            </div>
            <p className="text-sm text-earth-600">Owner Responses</p>
          </div>
        </div>
      </div>
    </div>
  )
}
