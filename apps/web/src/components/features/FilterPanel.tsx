'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/Button'
import { Badge } from '@/components/ui/Badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import { formatNumber } from '@/lib/utils'
import {
  SOUTH_AFRICAN_PROVINCES,
  SouthAfricanProvince,
  FARM_AMENITIES,
  FarmAmenity,
  GAME_SPECIES,
  GameSpeciesName
} from '@/lib/constants'

export interface FilterOptions {
  activities: ('hunting' | 'photo_safari')[]
  provinces: SouthAfricanProvince[]
  priceRange: [number, number]
  amenities: FarmAmenity[]
  species: GameSpeciesName[]
}

export interface FilterPanelProps {
  filters: FilterOptions
  onFiltersChange: (filters: FilterOptions) => void
  className?: string
}





export function FilterPanel({ filters, onFiltersChange, className }: FilterPanelProps) {
  const [isExpanded, setIsExpanded] = useState(false)

  const handleActivityToggle = (activity: 'hunting' | 'photo_safari') => {
    const newActivities = filters.activities.includes(activity)
      ? filters.activities.filter(a => a !== activity)
      : [...filters.activities, activity]

    onFiltersChange({ ...filters, activities: newActivities })
  }

  const handleProvinceToggle = (province: SouthAfricanProvince) => {
    const newProvinces = filters.provinces.includes(province)
      ? filters.provinces.filter(p => p !== province)
      : [...filters.provinces, province]

    onFiltersChange({ ...filters, provinces: newProvinces })
  }

  const handleAmenityToggle = (amenity: FarmAmenity) => {
    const newAmenities = filters.amenities.includes(amenity)
      ? filters.amenities.filter(a => a !== amenity)
      : [...filters.amenities, amenity]

    onFiltersChange({ ...filters, amenities: newAmenities })
  }

  const handleSpeciesToggle = (speciesName: GameSpeciesName) => {
    const newSpecies = filters.species.includes(speciesName)
      ? filters.species.filter(s => s !== speciesName)
      : [...filters.species, speciesName]

    onFiltersChange({ ...filters, species: newSpecies })
  }

  const clearFilters = () => {
    onFiltersChange({
      activities: [],
      provinces: [],
      priceRange: [0, 20000],
      amenities: [],
      species: []
    })
  }

  const hasActiveFilters = filters.activities.length > 0 ||
    filters.provinces.length > 0 ||
    filters.amenities.length > 0 ||
    filters.species.length > 0

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle>Filters</CardTitle>
          <div className="flex items-center gap-2">
            {hasActiveFilters && (
              <Button variant="outline" size="sm" onClick={clearFilters}>
                Clear All
              </Button>
            )}
            <Button
              variant="outline"
              size="sm"
              onClick={() => setIsExpanded(!isExpanded)}
            >
              {isExpanded ? 'Less' : 'More'} Filters
            </Button>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Activity Type */}
        <div>
          <h4 className="font-semibold mb-3 text-earth-900">Activity Type</h4>
          <div className="flex flex-wrap gap-2">
            <button
              onClick={() => handleActivityToggle('hunting')}
              className={`px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                filters.activities.includes('hunting')
                  ? 'bg-hunting-accent text-white'
                  : 'bg-earth-200 text-earth-700 hover:bg-earth-300'
              }`}
            >
              Hunting
            </button>
            <button
              onClick={() => handleActivityToggle('photo_safari')}
              className={`px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                filters.activities.includes('photo_safari')
                  ? 'bg-photo-accent text-white'
                  : 'bg-earth-200 text-earth-700 hover:bg-earth-300'
              }`}
            >
              Photo Safari
            </button>
          </div>
        </div>

        {/* Price Range */}
        <div>
          <h4 className="font-semibold mb-3 text-earth-900">Price Range (per day)</h4>
          <div className="space-y-2">
            <input
              type="range"
              min="0"
              max="20000"
              step="500"
              value={filters.priceRange[1]}
              onChange={(e) => onFiltersChange({
                ...filters,
                priceRange: [filters.priceRange[0], parseInt(e.target.value)]
              })}
              className="w-full"
            />
            <div className="flex justify-between text-sm text-earth-600">
              <span>R0</span>
              <span>R{formatNumber(filters.priceRange[1])}</span>
            </div>
          </div>
        </div>

        {/* Provinces */}
        <div>
          <h4 className="font-semibold mb-3 text-earth-900">Provinces</h4>
          <div className="flex flex-wrap gap-2">
            {SOUTH_AFRICAN_PROVINCES.map((province) => (
              <button
                key={province}
                onClick={() => handleProvinceToggle(province)}
                className={`px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                  filters.provinces.includes(province)
                    ? 'bg-accent-600 text-white'
                    : 'bg-earth-200 text-earth-700 hover:bg-earth-300'
                }`}
              >
                {province}
              </button>
            ))}
          </div>
        </div>

        {isExpanded && (
          <>
            {/* Amenities */}
            <div>
              <h4 className="font-semibold mb-3 text-earth-900">Amenities</h4>
              <div className="flex flex-wrap gap-2">
                {FARM_AMENITIES.map((amenity) => (
                  <button
                    key={amenity}
                    onClick={() => handleAmenityToggle(amenity)}
                    className={`px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                      filters.amenities.includes(amenity)
                        ? 'bg-accent-600 text-white'
                        : 'bg-earth-200 text-earth-700 hover:bg-earth-300'
                    }`}
                  >
                    {amenity}
                  </button>
                ))}
              </div>
            </div>

            {/* Species */}
            <div>
              <h4 className="font-semibold mb-3 text-earth-900">Available Species</h4>
              <div className="flex flex-wrap gap-2">
                {GAME_SPECIES.map((speciesName) => (
                  <button
                    key={speciesName}
                    onClick={() => handleSpeciesToggle(speciesName)}
                    className={`px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                      filters.species.includes(speciesName)
                        ? 'bg-accent-600 text-white'
                        : 'bg-earth-200 text-earth-700 hover:bg-earth-300'
                    }`}
                  >
                    {speciesName}
                  </button>
                ))}
              </div>
            </div>
          </>
        )}

        {/* Active Filters Summary */}
        {hasActiveFilters && (
          <div>
            <h4 className="font-semibold mb-3 text-earth-900">Active Filters</h4>
            <div className="flex flex-wrap gap-2">
              {filters.activities.map((activity) => (
                <Badge key={activity} variant={activity === 'hunting' ? 'hunting' : 'photo'}>
                  {activity === 'hunting' ? 'Hunting' : 'Photo Safari'}
                </Badge>
              ))}
              {filters.provinces.map((province) => (
                <Badge key={province} variant="location">
                  📍 {province}
                </Badge>
              ))}
              {filters.amenities.slice(0, 3).map((amenity) => (
                <Badge key={amenity} variant="default">
                  {amenity}
                </Badge>
              ))}
              {filters.amenities.length > 3 && (
                <Badge variant="default">
                  +{filters.amenities.length - 3} more
                </Badge>
              )}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}