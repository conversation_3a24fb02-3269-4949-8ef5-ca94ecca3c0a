import { ButtonHTMLAttributes, forwardRef } from 'react'
import { cn } from '@/lib/utils'

export interface ButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'outline' | 'hunting' | 'photo'
  size?: 'sm' | 'md' | 'lg'
  isLoading?: boolean
}

const Button = forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant = 'primary', size = 'md', isLoading, children, disabled, ...props }, ref) => {
    const baseStyles = `
      inline-flex items-center justify-center rounded-[var(--radius-md)] 
      font-[var(--font-ui)] font-semibold transition-all duration-300 
      focus:outline-none focus:ring-2 focus:ring-offset-2
      disabled:opacity-50 disabled:cursor-not-allowed
      hover:transform hover:-translate-y-0.5 hover:shadow-[var(--shadow-lg)]
    `

    const variants = {
      primary: `
        bg-[var(--primary-brown)] text-white 
        hover:bg-[#A0522D] focus:ring-[var(--primary-brown)]
      `,
      secondary: `
        bg-[var(--secondary-sky)] text-white 
        hover:bg-[#357AE0] focus:ring-[var(--secondary-sky)]
      `,
      outline: `
        bg-transparent border-2 border-[var(--primary-brown)] text-[var(--primary-brown)]
        hover:bg-[var(--primary-brown)] hover:text-white focus:ring-[var(--primary-brown)]
      `,
      hunting: `
        bg-[var(--hunting-accent)] text-white 
        hover:bg-[#A67C00] focus:ring-[var(--hunting-accent)]
      `,
      photo: `
        bg-[var(--photo-accent)] text-white 
        hover:bg-[#3557C7] focus:ring-[var(--photo-accent)]
      `
    }

    const sizes = {
      sm: 'px-3 py-1.5 text-sm',
      md: 'px-6 py-2 text-base',
      lg: 'px-8 py-3 text-lg'
    }

    return (
      <button
        className={cn(
          baseStyles,
          variants[variant],
          sizes[size],
          isLoading && 'cursor-wait',
          className
        )}
        disabled={disabled || isLoading}
        ref={ref}
        {...props}
      >
        {isLoading && (
          <svg
            className="mr-2 h-4 w-4 animate-spin"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
          >
            <circle
              className="opacity-25"
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              strokeWidth="4"
            />
            <path
              className="opacity-75"
              fill="currentColor"
              d="m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            />
          </svg>
        )}
        {children}
      </button>
    )
  }
)

Button.displayName = 'Button'

export { Button }