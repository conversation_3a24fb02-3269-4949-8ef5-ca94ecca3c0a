import { HTMLAttributes, forwardRef } from 'react'
import { cn } from '@/lib/utils'

export interface BadgeProps extends HTMLAttributes<HTMLSpanElement> {
  variant?: 'hunting' | 'photo' | 'location' | 'default'
}

const Badge = forwardRef<HTMLSpanElement, BadgeProps>(
  ({ className, variant = 'default', ...props }, ref) => {
    const variants = {
      hunting: 'bg-[var(--hunting-accent)] text-white',
      photo: 'bg-[var(--photo-accent)] text-white',
      location: 'bg-[var(--secondary-stone)] text-white',
      default: 'bg-[var(--medium-gray)] text-white'
    }

    return (
      <span
        ref={ref}
        className={cn(
          'inline-flex items-center px-2 py-1 rounded-[var(--radius-sm)] text-sm font-semibold',
          variants[variant],
          className
        )}
        {...props}
      />
    )
  }
)

Badge.displayName = 'Badge'

export { Badge }