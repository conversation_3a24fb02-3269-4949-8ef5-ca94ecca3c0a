import { HTMLAttributes, forwardRef } from 'react'
import Image from 'next/image'
import { cn } from '@/lib/utils'

export interface CardProps extends HTMLAttributes<HTMLDivElement> {
  hover?: boolean
}

const Card = forwardRef<HTMLDivElement, CardProps>(
  ({ className, hover = true, children, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn(
          `
          bg-white rounded-[var(--radius-lg)] shadow-[var(--shadow-md)] 
          overflow-hidden transition-all duration-300
          `,
          hover && 'hover:transform hover:-translate-y-1 hover:shadow-[var(--shadow-xl)]',
          className
        )}
        {...props}
      >
        {children}
      </div>
    )
  }
)

Card.displayName = 'Card'

const CardContent = forwardRef<HTMLDivElement, HTMLAttributes<HTMLDivElement>>(
  ({ className, ...props }, ref) => (
    <div
      ref={ref}
      className={cn('p-[var(--space-lg)]', className)}
      {...props}
    />
  )
)

CardContent.displayName = 'CardContent'

const CardHeader = forwardRef<HTMLDivElement, HTMLAttributes<HTMLDivElement>>(
  ({ className, ...props }, ref) => (
    <div
      ref={ref}
      className={cn('px-[var(--space-lg)] pt-[var(--space-lg)]', className)}
      {...props}
    />
  )
)

CardHeader.displayName = 'CardHeader'

const CardTitle = forwardRef<HTMLHeadingElement, HTMLAttributes<HTMLHeadingElement>>(
  ({ className, ...props }, ref) => (
    <h3
      ref={ref}
      className={cn(
        'text-xl font-bold mb-2 text-[var(--primary-brown)] font-[var(--font-display)]',
        className
      )}
      {...props}
    />
  )
)

CardTitle.displayName = 'CardTitle'

const CardImage = forwardRef<HTMLDivElement, HTMLAttributes<HTMLDivElement> & {
  src?: string
  alt?: string
  children?: React.ReactNode
}>(
  ({ className, src, alt, children, ...props }, ref) => (
    <div
      ref={ref}
      className={cn(
        'relative w-full h-48 bg-gradient-to-br from-[var(--primary-green)] to-[var(--secondary-sunset)] flex items-center justify-center text-white text-5xl',
        className
      )}
      {...props}
    >
      {src ? (
        <Image src={src} alt={alt || ''} fill className="object-cover" />
      ) : (
        children
      )}
    </div>
  )
)

CardImage.displayName = 'CardImage'

export { Card, CardContent, CardHeader, CardTitle, CardImage }