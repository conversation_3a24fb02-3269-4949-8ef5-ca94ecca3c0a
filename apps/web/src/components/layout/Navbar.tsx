'use client'

import { useState } from 'react'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/Button'
import { cn } from '@/lib/utils'
import { useAuth } from '@/hooks/useAuth'

export function Navbar() {
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [isUserMenuOpen, setIsUserMenuOpen] = useState(false)
  const { user, loading, signOut } = useAuth()
  const router = useRouter()

  const handleSignOut = async () => {
    await signOut()
    router.push('/')
  }

  return (
    <nav className="bg-white shadow-[var(--shadow-sm)] sticky top-0 z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <Link
            href="/"
            className="flex items-center font-[var(--font-display)] text-xl font-bold text-[var(--primary-brown)]"
          >
            RvB Safaris
          </Link>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-8">
            <Link
              href="/farms"
              className="text-[var(--dark-gray)] hover:text-[var(--primary-brown)] font-medium transition-colors"
            >
              Farms
            </Link>
            <Link
              href="/farms?activity=hunting"
              className="text-[var(--dark-gray)] hover:text-[var(--primary-brown)] font-medium transition-colors"
            >
              Hunt
            </Link>
            <Link
              href="/farms?activity=photo_safari"
              className="text-[var(--dark-gray)] hover:text-[var(--primary-brown)] font-medium transition-colors"
            >
              Photo Safari
            </Link>
            <Link
              href="/about"
              className="text-[var(--dark-gray)] hover:text-[var(--primary-brown)] font-medium transition-colors"
            >
              About
            </Link>
          </div>

          {/* Auth Buttons */}
          <div className="hidden md:flex items-center space-x-4">
            {loading ? (
              <div className="animate-pulse bg-gray-200 h-8 w-20 rounded"></div>
            ) : user ? (
              <div className="relative">
                <button
                  onClick={() => setIsUserMenuOpen(!isUserMenuOpen)}
                  className="flex items-center space-x-2 text-[var(--dark-gray)] hover:text-[var(--primary-brown)] font-medium transition-colors"
                >
                  <div className="w-8 h-8 bg-[var(--primary-brown)] text-white rounded-full flex items-center justify-center text-sm font-semibold">
                    {user.displayName?.[0] || user.email?.[0]?.toUpperCase() || 'U'}
                  </div>
                  <span className="hidden lg:block">
                    {user.displayName || 'Account'}
                  </span>
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                  </svg>
                </button>

                {isUserMenuOpen && (
                  <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50 border">
                    <Link
                      href="/dashboard"
                      className="block px-4 py-2 text-sm text-[var(--dark-gray)] hover:bg-[var(--earth-100)] hover:text-[var(--primary-brown)]"
                      onClick={() => setIsUserMenuOpen(false)}
                    >
                      Dashboard
                    </Link>
                    <Link
                      href="/profile"
                      className="block px-4 py-2 text-sm text-[var(--dark-gray)] hover:bg-[var(--earth-100)] hover:text-[var(--primary-brown)]"
                      onClick={() => setIsUserMenuOpen(false)}
                    >
                      Profile Settings
                    </Link>
                    <hr className="my-1" />
                    <button
                      onClick={() => {
                        setIsUserMenuOpen(false)
                        handleSignOut()
                      }}
                      className="block w-full text-left px-4 py-2 text-sm text-[var(--dark-gray)] hover:bg-[var(--earth-100)] hover:text-[var(--primary-brown)]"
                    >
                      Sign Out
                    </button>
                  </div>
                )}
              </div>
            ) : (
              <>
                <Link href="/auth/login">
                  <Button variant="outline" size="sm">
                    Sign In
                  </Button>
                </Link>
                <Link href="/auth/register">
                  <Button variant="primary" size="sm">
                    Sign Up
                  </Button>
                </Link>
              </>
            )}
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden">
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="text-[var(--dark-gray)] hover:text-[var(--primary-brown)] p-2"
            >
              <svg
                className="h-6 w-6"
                fill="none"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                {isMenuOpen ? (
                  <path d="M6 18L18 6M6 6l12 12" />
                ) : (
                  <path d="M4 6h16M4 12h16M4 18h16" />
                )}
              </svg>
            </button>
          </div>
        </div>

        {/* Mobile Navigation */}
        <div
          className={cn(
            'md:hidden transition-all duration-300 overflow-hidden',
            isMenuOpen ? 'max-h-96 pb-4' : 'max-h-0'
          )}
        >
          <div className="px-2 pt-2 pb-3 space-y-1">
            <Link
              href="/farms"
              className="block px-3 py-2 text-[var(--dark-gray)] hover:text-[var(--primary-brown)] font-medium"
            >
              Farms
            </Link>
            <Link
              href="/farms?activity=hunting"
              className="block px-3 py-2 text-[var(--dark-gray)] hover:text-[var(--primary-brown)] font-medium"
            >
              Hunt
            </Link>
            <Link
              href="/farms?activity=photo_safari"
              className="block px-3 py-2 text-[var(--dark-gray)] hover:text-[var(--primary-brown)] font-medium"
            >
              Photo Safari
            </Link>
            <Link
              href="/about"
              className="block px-3 py-2 text-[var(--dark-gray)] hover:text-[var(--primary-brown)] font-medium"
            >
              About
            </Link>
            <div className="pt-4 space-y-2">
              {loading ? (
                <div className="animate-pulse bg-gray-200 h-8 w-full rounded"></div>
              ) : user ? (
                <>
                  <Link href="/dashboard" className="block">
                    <Button variant="outline" size="sm" className="w-full">
                      Dashboard
                    </Button>
                  </Link>
                  <Link href="/profile" className="block">
                    <Button variant="outline" size="sm" className="w-full">
                      Profile
                    </Button>
                  </Link>
                  <Button variant="primary" size="sm" className="w-full" onClick={handleSignOut}>
                    Sign Out
                  </Button>
                </>
              ) : (
                <>
                  <Link href="/auth/login" className="block">
                    <Button variant="outline" size="sm" className="w-full">
                      Sign In
                    </Button>
                  </Link>
                  <Link href="/auth/register" className="block">
                    <Button variant="primary" size="sm" className="w-full">
                      Sign Up
                    </Button>
                  </Link>
                </>
              )}
            </div>
          </div>
        </div>
      </div>
    </nav>
  )
}