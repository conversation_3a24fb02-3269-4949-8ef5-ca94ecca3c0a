'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/Button'
import { Card } from '@/components/ui/Card'
import { debugAuthState, refreshUserToken } from '@/lib/utils/debugAuth'
import { useAuth } from '@/hooks/useAuth'

interface AuthDebugInfo {
  uid: string
  email: string | null
  role: string
  hasRole: boolean
  claims: Record<string, unknown>
}

export function AuthDebugPanel() {
  const [debugInfo, setDebugInfo] = useState<AuthDebugInfo | null>(null)
  const [isDebugging, setIsDebugging] = useState(false)
  const [isRefreshing, setIsRefreshing] = useState(false)
  const { user } = useAuth()

  const handleDebugAuth = async () => {
    setIsDebugging(true)
    try {
      const info = await debugAuthState()
      setDebugInfo(info)
    } catch (error) {
      console.error('Debug failed:', error)
    } finally {
      setIsDebugging(false)
    }
  }

  const handleRefreshToken = async () => {
    setIsRefreshing(true)
    try {
      await refreshUserToken()
      // Re-run debug after refresh
      const info = await debugAuthState()
      setDebugInfo(info)
    } catch (error) {
      console.error('Token refresh failed:', error)
    } finally {
      setIsRefreshing(false)
    }
  }

  const handleSetRole = async () => {
    if (!user) return

    try {
      const idToken = await user.getIdToken()
      const response = await fetch('/api/auth/set-role', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${idToken}`,
        },
        body: JSON.stringify({
          uid: user.uid,
          role: 'guest' // Default to guest role
        }),
      })

      if (response.ok) {
        console.log('Role set successfully')
        // Refresh token to get new claims
        await handleRefreshToken()
      } else {
        const errorData = await response.json()
        console.error('Failed to set role:', errorData.error)
      }
    } catch (error) {
      console.error('Error setting role:', error)
    }
  }

  if (!user) {
    return null
  }

  return (
    <Card className="p-4 mb-4 bg-yellow-50 border-yellow-200">
      <h3 className="text-lg font-semibold text-yellow-800 mb-3">
        🔍 Authentication Debug Panel
      </h3>
      
      <div className="space-y-3">
        <div className="flex gap-2">
          <Button
            onClick={handleDebugAuth}
            variant="outline"
            size="sm"
            isLoading={isDebugging}
          >
            Check Auth State
          </Button>
          
          <Button
            onClick={handleRefreshToken}
            variant="outline"
            size="sm"
            isLoading={isRefreshing}
          >
            Refresh Token
          </Button>

          <Button
            onClick={handleSetRole}
            variant="outline"
            size="sm"
          >
            Set Role (Guest)
          </Button>
        </div>

        {debugInfo && (
          <div className="bg-white p-3 rounded border text-sm">
            <div className="grid grid-cols-2 gap-2">
              <div><strong>UID:</strong> {debugInfo.uid}</div>
              <div><strong>Email:</strong> {debugInfo.email}</div>
              <div><strong>Role:</strong> {debugInfo.role || 'NOT SET'}</div>
              <div><strong>Has Role:</strong> {debugInfo.hasRole ? '✅' : '❌'}</div>
            </div>
            
            {!debugInfo.hasRole && (
              <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded">
                <p className="text-red-700 text-xs">
                  ⚠️ No role set in custom claims! This will cause permission errors.
                  Click &quot;Set Role (Guest)&quot; to fix this issue.
                </p>
              </div>
            )}
            
            <details className="mt-2">
              <summary className="cursor-pointer text-xs text-gray-600">
                View All Claims
              </summary>
              <pre className="text-xs bg-gray-100 p-2 rounded mt-1 overflow-auto">
                {JSON.stringify(debugInfo.claims, null, 2)}
              </pre>
            </details>
          </div>
        )}
      </div>
    </Card>
  )
}
