<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Safari Booking App - Design System & Style Guide</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            /* Primary Colors - Earth-inspired palette */
            --primary-brown: #8B4513;
            --primary-gold: #DAA520;
            --primary-green: #556B2F;
            
            /* Secondary Colors - Wildlife tones */
            --secondary-sunset: #FF6B35;
            --secondary-sky: #4A90E2;
            --secondary-stone: #8D8B7A;
            --secondary-cream: #F5F5DC;
            
            /* Activity Differentiation */
            --hunting-accent: #B8860B;
            --photo-accent: #4169E1;
            
            /* Neutrals */
            --white: #FFFFFF;
            --light-gray: #F8F9FA;
            --medium-gray: #6C757D;
            --dark-gray: #343A40;
            --black: #000000;
            
            /* Typography */
            --font-display: 'Georgia', serif;
            --font-body: 'Segoe UI', system-ui, sans-serif;
            --font-ui: 'Inter', -apple-system, sans-serif;
            
            /* Spacing */
            --space-xs: 0.25rem;
            --space-sm: 0.5rem;
            --space-md: 1rem;
            --space-lg: 1.5rem;
            --space-xl: 2rem;
            --space-xxl: 3rem;
            
            /* Border Radius */
            --radius-sm: 4px;
            --radius-md: 8px;
            --radius-lg: 12px;
            --radius-xl: 16px;
            
            /* Shadows */
            --shadow-sm: 0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24);
            --shadow-md: 0 4px 6px rgba(0,0,0,0.07), 0 1px 3px rgba(0,0,0,0.06);
            --shadow-lg: 0 10px 15px rgba(0,0,0,0.1), 0 4px 6px rgba(0,0,0,0.05);
            --shadow-xl: 0 20px 25px rgba(0,0,0,0.15), 0 10px 10px rgba(0,0,0,0.04);
        }

        body {
            font-family: var(--font-body);
            line-height: 1.6;
            color: var(--dark-gray);
            background: var(--light-gray);
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 var(--space-md);
        }

        /* Typography System */
        h1, h2, h3, h4, h5, h6 {
            font-family: var(--font-display);
            font-weight: bold;
            line-height: 1.2;
            margin-bottom: var(--space-md);
        }

        h1 { font-size: 3rem; color: var(--primary-brown); }
        h2 { font-size: 2.5rem; color: var(--primary-brown); }
        h3 { font-size: 2rem; color: var(--primary-green); }
        h4 { font-size: 1.5rem; }
        h5 { font-size: 1.25rem; }
        h6 { font-size: 1rem; }

        .section {
            background: var(--white);
            margin: var(--space-xl) 0;
            padding: var(--space-xxl);
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-md);
        }

        /* Header */
        .header {
            background: linear-gradient(135deg, var(--primary-brown) 0%, var(--primary-green) 100%);
            color: var(--white);
            padding: var(--space-xxl) 0;
            text-align: center;
            margin-bottom: 0;
        }

        .header h1 {
            color: var(--white);
            margin-bottom: var(--space-sm);
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
            max-width: 600px;
            margin: 0 auto;
        }

        /* Color Palette Section */
        .color-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: var(--space-lg);
            margin-top: var(--space-lg);
        }

        .color-card {
            text-align: center;
        }

        .color-swatch {
            width: 100%;
            height: 100px;
            border-radius: var(--radius-md);
            margin-bottom: var(--space-sm);
            box-shadow: var(--shadow-sm);
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--white);
            font-weight: bold;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
        }

        .primary-brown { background: var(--primary-brown); }
        .primary-gold { background: var(--primary-gold); }
        .primary-green { background: var(--primary-green); }
        .secondary-sunset { background: var(--secondary-sunset); }
        .secondary-sky { background: var(--secondary-sky); }
        .secondary-stone { background: var(--secondary-stone); }
        .hunting-accent { background: var(--hunting-accent); }
        .photo-accent { background: var(--photo-accent); }

        /* Typography Showcase */
        .typography-showcase {
            display: grid;
            gap: var(--space-lg);
        }

        .font-example {
            padding: var(--space-lg);
            border: 1px solid var(--medium-gray);
            border-radius: var(--radius-md);
            background: var(--secondary-cream);
        }

        /* Component Library */
        .component-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: var(--space-xl);
            margin-top: var(--space-lg);
        }

        /* Buttons */
        .btn {
            display: inline-block;
            padding: var(--space-sm) var(--space-lg);
            border: none;
            border-radius: var(--radius-md);
            font-family: var(--font-ui);
            font-weight: 600;
            text-decoration: none;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
            margin: var(--space-xs);
        }

        .btn-primary {
            background: var(--primary-brown);
            color: var(--white);
        }

        .btn-primary:hover {
            background: #A0522D;
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .btn-secondary {
            background: var(--secondary-sky);
            color: var(--white);
        }

        .btn-hunting {
            background: var(--hunting-accent);
            color: var(--white);
        }

        .btn-photo {
            background: var(--photo-accent);
            color: var(--white);
        }

        .btn-outline {
            background: transparent;
            border: 2px solid var(--primary-brown);
            color: var(--primary-brown);
        }

        /* Cards */
        .card {
            background: var(--white);
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-md);
            overflow: hidden;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .card:hover {
            transform: translateY(-4px);
            box-shadow: var(--shadow-xl);
        }

        .card-image {
            width: 100%;
            height: 200px;
            background: linear-gradient(45deg, var(--primary-green), var(--secondary-sunset));
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--white);
            font-size: 3rem;
        }

        .card-content {
            padding: var(--space-lg);
        }

        .card-title {
            font-size: 1.25rem;
            font-weight: bold;
            margin-bottom: var(--space-sm);
            color: var(--primary-brown);
        }

        .card-meta {
            display: flex;
            gap: var(--space-sm);
            margin-bottom: var(--space-md);
        }

        .badge {
            padding: var(--space-xs) var(--space-sm);
            border-radius: var(--radius-sm);
            font-size: 0.875rem;
            font-weight: 600;
        }

        .badge-hunting {
            background: var(--hunting-accent);
            color: var(--white);
        }

        .badge-photo {
            background: var(--photo-accent);
            color: var(--white);
        }

        .badge-location {
            background: var(--secondary-stone);
            color: var(--white);
        }

        /* Form Elements */
        .form-group {
            margin-bottom: var(--space-lg);
        }

        .form-label {
            display: block;
            margin-bottom: var(--space-sm);
            font-weight: 600;
            color: var(--primary-brown);
        }

        .form-input {
            width: 100%;
            padding: var(--space-sm) var(--space-md);
            border: 2px solid var(--medium-gray);
            border-radius: var(--radius-md);
            font-family: var(--font-ui);
            transition: border-color 0.3s ease;
        }

        .form-input:focus {
            outline: none;
            border-color: var(--primary-brown);
            box-shadow: 0 0 0 3px rgba(139, 69, 19, 0.1);
        }

        /* Navigation */
        .nav {
            background: var(--white);
            box-shadow: var(--shadow-sm);
            padding: var(--space-md) 0;
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .nav-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .nav-logo {
            font-family: var(--font-display);
            font-size: 1.5rem;
            font-weight: bold;
            color: var(--primary-brown);
            text-decoration: none;
        }

        .nav-links {
            display: flex;
            gap: var(--space-lg);
            list-style: none;
        }

        .nav-link {
            color: var(--dark-gray);
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s ease;
        }

        .nav-link:hover {
            color: var(--primary-brown);
        }

        /* Icons */
        .icon-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: var(--space-lg);
            text-align: center;
        }

        .icon-item {
            padding: var(--space-lg);
            border-radius: var(--radius-md);
            background: var(--secondary-cream);
        }

        .icon-item i {
            font-size: 2rem;
            color: var(--primary-brown);
            margin-bottom: var(--space-sm);
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            h1 { font-size: 2rem; }
            h2 { font-size: 1.75rem; }
            
            .container {
                padding: 0 var(--space-sm);
            }
            
            .section {
                padding: var(--space-lg);
                margin: var(--space-lg) 0;
            }
            
            .nav-links {
                display: none;
            }
            
            .component-grid {
                grid-template-columns: 1fr;
            }
        }

        /* Animation Classes */
        .fade-in {
            animation: fadeIn 0.6s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .slide-up:hover {
            transform: translateY(-2px);
            transition: transform 0.3s ease;
        }

        /* Search Bar */
        .search-container {
            position: relative;
            max-width: 500px;
            margin: var(--space-lg) auto;
        }

        .search-input {
            width: 100%;
            padding: var(--space-md) var(--space-xl) var(--space-md) var(--space-lg);
            border: 3px solid var(--primary-brown);
            border-radius: var(--radius-xl);
            font-size: 1.1rem;
            background: var(--white);
        }

        .search-btn {
            position: absolute;
            right: var(--space-sm);
            top: 50%;
            transform: translateY(-50%);
            background: var(--primary-brown);
            color: var(--white);
            border: none;
            padding: var(--space-sm) var(--space-md);
            border-radius: var(--radius-md);
            cursor: pointer;
        }

        /* Filter Pills */
        .filter-container {
            display: flex;
            flex-wrap: wrap;
            gap: var(--space-sm);
            margin: var(--space-lg) 0;
        }

        .filter-pill {
            padding: var(--space-sm) var(--space-md);
            border: 2px solid var(--medium-gray);
            border-radius: var(--radius-xl);
            background: var(--white);
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .filter-pill:hover, .filter-pill.active {
            border-color: var(--primary-brown);
            background: var(--primary-brown);
            color: var(--white);
        }

        /* Dashboard Stats */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: var(--space-lg);
        }

        .stat-card {
            background: linear-gradient(135deg, var(--primary-brown), var(--primary-gold));
            color: var(--white);
            padding: var(--space-lg);
            border-radius: var(--radius-lg);
            text-align: center;
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            display: block;
        }

        .stat-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="nav">
        <div class="container">
            <div class="nav-container">
                <a href="#" class="nav-logo">🦁 RvB Safaris</a>
                <ul class="nav-links">
                    <li><a href="#" class="nav-link">Hunt</a></li>
                    <li><a href="#" class="nav-link">Photo Safari</a></li>
                    <li><a href="#" class="nav-link">Farms</a></li>
                    <li><a href="#" class="nav-link">About</a></li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Header -->
    <header class="header">
        <div class="container">
            <h1>RvB Safaris Design System</h1>
            <p>A comprehensive style guide for South Africa's premier hunt and photo safari booking platform</p>
        </div>
    </header>

    <div class="container">
        <!-- Color Palette -->
        <section class="section fade-in">
            <h2>Color Palette</h2>
            <p>Our earth-inspired color system reflects the natural beauty of South African landscapes while maintaining accessibility and brand distinction.</p>
            
            <h3>Primary Colors</h3>
            <div class="color-grid">
                <div class="color-card">
                    <div class="color-swatch primary-brown">Safari Brown</div>
                    <strong>#8B4513</strong>
                    <p>Primary brand color</p>
                </div>
                <div class="color-card">
                    <div class="color-swatch primary-gold">Grassland Gold</div>
                    <strong>#DAA520</strong>
                    <p>Accent & highlights</p>
                </div>
                <div class="color-card">
                    <div class="color-swatch primary-green">Acacia Green</div>
                    <strong>#556B2F</strong>
                    <p>Nature & growth</p>
                </div>
            </div>

            <h3>Activity Differentiation</h3>
            <div class="color-grid">
                <div class="color-card">
                    <div class="color-swatch hunting-accent">Hunting</div>
                    <strong>#B8860B</strong>
                    <p>Hunting experiences</p>
                </div>
                <div class="color-card">
                    <div class="color-swatch photo-accent">Photo Safari</div>
                    <strong>#4169E1</strong>
                    <p>Photography experiences</p>
                </div>
            </div>

            <h3>Secondary Colors</h3>
            <div class="color-grid">
                <div class="color-card">
                    <div class="color-swatch secondary-sunset">Sunset Orange</div>
                    <strong>#FF6B35</strong>
                </div>
                <div class="color-card">
                    <div class="color-swatch secondary-sky">Sky Blue</div>
                    <strong>#4A90E2</strong>
                </div>
                <div class="color-card">
                    <div class="color-swatch secondary-stone">Stone Gray</div>
                    <strong>#8D8B7A</strong>
                </div>
            </div>
        </section>

        <!-- Typography -->
        <section class="section fade-in">
            <h2>Typography System</h2>
            <div class="typography-showcase">
                <div class="font-example">
                    <h3>Display Font - Georgia Serif</h3>
                    <h1>Adventure Awaits in the African Wilderness</h1>
                    <h2>Discover Your Perfect Safari Experience</h2>
                    <h3>Premium Game Farms & Lodges</h3>
                    <p><strong>Usage:</strong> Headlines, brand elements, and premium content that evokes safari heritage.</p>
                </div>
                
                <div class="font-example">
                    <h4>Body Font - Segoe UI Sans-serif</h4>
                    <p>This is our primary body text font, designed for excellent readability across all devices. It works perfectly for detailed farm descriptions, booking information, user reviews, and all content that requires clear communication. The font maintains legibility at various sizes while providing a modern, trustworthy appearance.</p>
                    <p><strong>Usage:</strong> All body text, descriptions, and content blocks.</p>
                </div>
                
                <div class="font-example">
                    <h5>UI Font - Inter Sans-serif</h5>
                    <p style="font-family: var(--font-ui);">This clean, modern font is used for all interface elements including buttons, navigation menus, form labels, and dashboard components. It ensures optimal legibility in small sizes and maintains consistency across the user interface.</p>
                    <p><strong>Usage:</strong> Navigation, buttons, forms, and UI components.</p>
                </div>
            </div>
        </section>

        <!-- Buttons & Controls -->
        <section class="section fade-in">
            <h2>Buttons & Interactive Elements</h2>
            <div class="component-grid">
                <div>
                    <h4>Primary Actions</h4>
                    <a href="#" class="btn btn-primary">Book Now</a>
                    <a href="#" class="btn btn-outline">Learn More</a>
                    <a href="#" class="btn btn-secondary">Contact Farm</a>
                </div>
                
                <div>
                    <h4>Activity-Specific Buttons</h4>
                    <a href="#" class="btn btn-hunting">🏹 Book Hunt</a>
                    <a href="#" class="btn btn-photo">📸 Photo Safari</a>
                </div>
            </div>

            <h3>Search Interface</h3>
            <div class="search-container">
                <input type="text" class="search-input" placeholder="Search farms, locations, or activities...">
                <button class="search-btn"><i class="fas fa-search"></i></button>
            </div>

            <h4>Filter System</h4>
            <div class="filter-container">
                <div class="filter-pill active">All</div>
                <div class="filter-pill">Hunting</div>
                <div class="filter-pill">Photo Safari</div>
                <div class="filter-pill">Big 5</div>
                <div class="filter-pill">Bow Hunting</div>
                <div class="filter-pill">Luxury Lodge</div>
                <div class="filter-pill">Eastern Cape</div>
            </div>
        </section>

        <!-- Form Elements -->
        <section class="section fade-in">
            <h2>Form Components</h2>
            <div class="component-grid">
                <div>
                    <h4>Booking Form Example</h4>
                    <div class="form-group">
                        <label class="form-label">Experience Type</label>
                        <select class="form-input">
                            <option>Select Activity</option>
                            <option>Hunting Safari</option>
                            <option>Photo Safari</option>
                            <option>Both Activities</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">Check-in Date</label>
                        <input type="date" class="form-input">
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">Number of Guests</label>
                        <input type="number" class="form-input" min="1" max="20" value="2">
                    </div>
                    
                    <button class="btn btn-primary" style="width: 100%;">Check Availability</button>
                </div>
                
                <div>
                    <h4>Contact Information</h4>
                    <div class="form-group">
                        <label class="form-label">Full Name</label>
                        <input type="text" class="form-input" placeholder="John Safari Enthusiast">
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">Email Address</label>
                        <input type="email" class="form-input" placeholder="<EMAIL>">
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">Special Requests</label>
                        <textarea class="form-input" rows="4" placeholder="Any specific animals you're hoping to see or photograph? Dietary requirements? Accessibility needs?"></textarea>
                    </div>
                </div>
            </div>
        </section>

        <!-- Card Components -->
        <section class="section fade-in">
            <h2>Farm Listing Cards</h2>
            <div class="component-grid">
                <div class="card slide-up">
                    <div class="card-image">
                        🦁
                    </div>
                    <div class="card-content">
                        <div class="card-title">Kruger Valley Game Reserve</div>
                        <div class="card-meta">
                            <span class="badge badge-hunting">Hunting</span>
                            <span class="badge badge-photo">Photo Safari</span>
                            <span class="badge badge-location">Limpopo</span>
                        </div>
                        <p>Experience the Big 5 in their natural habitat. Professional guides, luxury accommodation, and unforgettable memories await.</p>
                        <div style="margin-top: var(--space-md);">
                            <strong>From R2,500/day</strong>
                            <div style="float: right;">
                                ⭐⭐⭐⭐⭐ (47 reviews)
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="card slide-up">
                    <div class="card-image">
                        📸
                    </div>
                    <div class="card-content">
                        <div class="card-title">Sunset Photography Lodge</div>
                        <div class="card-meta">
                            <span class="badge badge-photo">Photo Safari</span>
                            <span class="badge badge-location">Eastern Cape</span>
                        </div>
                        <p>Specialized photography safaris with professional wildlife photographers as guides. Perfect lighting, rare species, premium hides.</p>
                        <div style="margin-top: var(--space-md);">
                            <strong>From R1,800/day</strong>
                            <div style="float: right;">
                                ⭐⭐⭐⭐⭐ (23 reviews)
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="card slide-up">
                    <div class="card-image">
                        🏹
                    </div>
                    <div class="card-content">
                        <div class="card-title">Bushveld Hunting Estate</div>
                        <div class="card-meta">
                            <span class="badge badge-hunting">Hunting</span>
                            <span class="badge badge-location">North West</span>
                        </div>
                        <p>Traditional South African hunting experience. Bow and rifle hunting available. Trophy preparation and taxidermy services on-site.</p>
                        <div style="margin-top: var(--space-md);">
                            <strong>From R3,200/day</strong>
                            <div style="float: right;">
                                ⭐⭐⭐⭐ (31 reviews)
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Icons & Visual Elements -->
        <section class="section fade-in">
            <h2>Iconography & Visual Elements</h2>
            <h3>Activity & Amenity Icons</h3>
            <div class="icon-grid">
                <div class="icon-item">
                    <i class="fas fa-crosshairs"></i>
                    <p><strong>Hunting</strong></p>
                </div>
                <div class="icon-item">
                    <i class="fas fa-camera"></i>
                    <p><strong>Photography</strong></p>
                </div>
                <div class="icon-item">
                    <i class="fas fa-bed"></i>
                    <p><strong>Accommodation</strong></p>
                </div>
                <div class="icon-item">
                    <i class="fas fa-utensils"></i>
                    <p><strong>Meals Included</strong></p>
                </div>
                <div class="icon-item">
                    <i class="fas fa-wifi"></i>
                    <p><strong>WiFi Available</strong></p>
                </div>
                <div class="icon-item">
                    <i class="fas fa-map-marked-alt"></i>
                    <p><strong>Guided Tours</strong></p>
                </div>
                <div class="icon-item">
                    <i class="fas fa-paw"></i>
                    <p><strong>Big 5</strong></p>
                </div>
                <div class="icon-item">
                    <i class="fas fa-award"></i>
                    <p><strong>Premium</strong></p>
                </div>
            </div>
        </section>

        <!-- Dashboard Components -->
        <section class="section fade-in">
            <h2>Dashboard Components</h2>
            <h3>Statistics Overview</h3>
            <div class="stats-grid">
                <div class="stat-card">
                    <span class="stat-number">247</span>
                    <span class="stat-label">Active Bookings</span>
                </div>
                <div class="stat-card">
                    <span class="stat-number">89%</span>
                    <span class="stat-label">Occupancy Rate</span>
                </div>
                <div class="stat-card">
                    <span class="stat-number">4.8</span>
                    <span class="stat-label">Average Rating</span>
                </div>
                <div class="stat-card">
                    <span class="stat-number">R152k</span>
                    <span class="stat-label">Monthly Revenue</span>
                </div>
            </div>
        </section>

        <!-- Responsive Design -->
        <section class="section fade-in">
            <h2>Responsive Design Principles</h2>
            <div class="component-grid">
                <div>
                    <h4>Mobile-First Approach</h4>
                    <p>Our design system prioritizes mobile experience with touch-friendly interfaces, optimized imagery, and streamlined navigation. Key actions like "Book Now" are prominently displayed and easily accessible.</p>
                </div>
            </div>
        </section>
    </div>
</body>
</html>